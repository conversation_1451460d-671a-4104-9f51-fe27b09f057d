version: "3.8"

networks:
  jedsy:
    external: true

services:
  db:
    image: postgres:15-alpine
    volumes:
      - postgres-data:/var/lib/postgresql/data
    environment:
      POSTGRES_USER: postgres
      POSTGRES_PASSWORD: ${POSTGRES_PASSWORD}
      POSTGRES_DB: ms-glider
    networks:
      - jedsy
    restart: always
  backend:
    build: 
      context: .
      dockerfile: Dockerfile
    environment:
      - SPRING_DATASOURCE_URL=${SPRING_DATASOURCE_URL}
      - SPRING_DATASOURCE_USERNAME=${SPRING_DATASOURCE_USERNAME}
      - SPRING_DATASOURCE_PASSWORD=${SPRING_DATASOURCE_PASSWORD}
      - FLYWAY_URL=${FLYWAY_URL}
      - FLYWAY_USER=${FLYWAY_USER}
      - FLYWAY_PASSWORD=${FLYWAY_PASSWORD}
      - SERVER_PORT=5000
    ports:
      - 5000:5000
    depends_on:
      - db
    networks:
      - jedsy
    restart: always

volumes:
  postgres-data:
