# @format
---
name: Package
on:
  push:
    branches:
      - "develop"
      - "main"
    tags:
      - "v*"
  pull_request:
    branches:
      - "develop"

jobs:
  build:
    runs-on: ubuntu-latest
    # If this is not given, the connection to the database is not working
    container: ubuntu
    services:
      postgres:
        image: postgres:15-alpine
        env:
          POSTGRES_PASSWORD: postgres
        options: >-
          --health-cmd pg_isready
          --health-interval 10s
          --health-timeout 5s
          --health-retries 5
    steps:
      - name: Checkout repository
        uses: actions/checkout@v3
      - uses: actions/setup-java@v3
        with:
          java-version: "17"
          distribution: "temurin"
          cache: "maven"
          server-id: "github"
          server-username: ACTOR_REF
          server-password: TOKEN_REF
      - name: Cache mvn packages
        uses: actions/cache@v3
        with:
          path: /root/.m2/repository
          key: ${{ runner.os }}-maven-${{ hashFiles('**/pom.xml') }}
          restore-keys: |
            ${{ runner.os }}-maven-
      - name: Remove cached internal dependencies
        run: rm -Rf /root/.m2/repository/com/jedsy/
      - name: Install package
        run: ./mvnw -s /github/home/<USER>/settings.xml --batch-mode -Dmaven.test.skip=true install
        env:
          GITHUB_TOKEN: ${{ secrets.GITHUB_TOKEN }}
          ACTOR_REF: dummy
          TOKEN_REF: ${{ secrets.GHCR_ORGANIZATION_TOKEN }}
          SPRING_DATASOURCE_URL: "**********************************************************************************"
      - name: Upload artifact
        uses: actions/upload-artifact@v4
        with:
          name: ${{ github.event.repository.name }}
          path: target/*.jar
          retention-days: 1
  docker:
    needs: build
    runs-on: ubuntu-latest
    outputs:
      image_digest: ${{ steps.build_push.outputs.digest }}
    permissions:
      id-token: write
      contents: read
    steps:
      - name: Checkout repository
        uses: actions/checkout@v3
      - name: Download artifact
        uses: actions/download-artifact@v4
        with:
          name: ${{ github.event.repository.name }}
          path: target/
      - name: Configure AWS credentials
        uses: aws-actions/configure-aws-credentials@v1-node16
        with:
          role-to-assume: ${{ secrets.AWS_ECR_PUSH_ROLE }}
          aws-region: ${{ vars.AWS_REGION }}
      - name: Login to Amazon ECR
        id: login-ecr
        uses: aws-actions/amazon-ecr-login@v1
      - name: Set up Docker Buildx
        uses: docker/setup-buildx-action@v2
        with:
          driver: docker
      - name: Extract metadata (tags, labels) for Docker
        id: meta
        uses: docker/metadata-action@v4
        with:
          images: ${{ vars.AWS_REGISTRY_URL }}
          tags: |
            # set latest tag for default branch
            type=raw,value=latest,enable={{is_default_branch}}
            type=ref,event=branch
            type=ref,event=pr
            type=semver,pattern={{version}}
            type=semver,pattern={{major}}.{{minor}}
      - id: build_push
        name: Build and push Docker image
        uses: docker/build-push-action@v4
        with:
          context: .
          push: true
          tags: ${{ steps.meta.outputs.tags }}
          labels: ${{ steps.meta.outputs.labels }}
  deploy:
    needs: docker
    runs-on: ubuntu-latest
    if: startsWith(github.ref, 'refs/tags/v') || github.ref == 'refs/heads/develop'
    steps:
      - if: github.ref == 'refs/heads/develop'
        run: echo "MS_ENV=dev" >> "$GITHUB_ENV"
      - if: startsWith(github.ref, 'refs/tags/v')
        run: echo "MS_ENV=prod" >> "$GITHUB_ENV"
      - name: Trigger workflow in IAC repo
        run: |
          curl \
          -sSL \
          --fail \
          -X POST \
          -H "Accept: application/vnd.github+json" \
          -H "Authorization: Bearer $IAC_WORKFLOW_TRIGGER_TOKEN" \
          -H "X-GitHub-Api-Version: 2022-11-28" \
          "https://api.github.com/repos/PackageGlider/iac/actions/workflows/deploy.yaml/dispatches" \
          -d "{\"ref\":\"main\",\"inputs\":{\"name\":\"$MS_NAME\",\"digest\":\"$MS_DIGEST\",\"environment\":\"$MS_ENV\"}}"
        env:
          IAC_WORKFLOW_TRIGGER_TOKEN: ${{ secrets.IAC_WORKFLOW_TRIGGER_TOKEN }}
          MS_NAME: ${{ github.event.repository.name }}
          MS_DIGEST: ${{ needs.docker.outputs.image_digest }}
