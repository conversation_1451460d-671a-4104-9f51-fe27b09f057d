---
name: Lint
on: push

jobs:
  build:
    name: Lint Code Base
    runs-on: ubuntu-latest
    steps:
      - name: Checkout Code
        uses: actions/checkout@v3
        with:
          # Full git history is needed to get a proper
          # list of changed files within `super-linter`
          fetch-depth: 0
      - name: Lint Code Base
        uses: github/super-linter@v5.0.0
        env:
          VALIDATE_ALL_CODEBASE: false
          DEFAULT_BRANCH: develop
          GITHUB_TOKEN: ${{ secrets.GITHUB_TOKEN }}
          FILTER_REGEX_EXCLUDE: mvnw
          LINTER_RULES_PATH: /
          TRIGGER: true
