FROM openjdk:17-jdk-alpine

# Create a group and user
RUN addgroup -g 1000 jedsy && adduser -D -u 1000 -G jedsy jedsy


# Set the working directory
WORKDIR /app

# Copy the source code into the Docker image
COPY . .

# Install curl and Maven
RUN apk add --no-cache curl maven

# Download and install Flyway
RUN curl -L https://repo1.maven.org/maven2/org/flywaydb/flyway-commandline/7.15.0/flyway-commandline-7.15.0.tar.gz | tar xvz && \
    ln -s `pwd`/flyway-7.15.0/flyway /usr/local/bin/flyway

COPY target/ms-glider-*.jar glider.jar

# Make the script executable
RUN chmod +x run_server.sh

EXPOSE 8080 5000

# Run the script as the entrypoint
CMD ["/bin/sh", "run_server.sh"]