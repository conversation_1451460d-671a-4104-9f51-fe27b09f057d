spring:
  application:
    name: ms-glider
  jpa:
    properties:
      hibernate:
        dialect: org.hibernate.dialect.PostgreSQLDialect
      javax:
        persistence:
          schema-generation:
            create-source: metadata
            scripts:
              action: update
              create-target: src/main/resources/update.sql
  datasource:
    url: ${SPRING_DATASOURCE_URL}
    username: ${SPRING_DATASOURCE_USERNAME}
    password: ${SPRING_DATASOURCE_PASSWORD}
    driver-class-name: org.postgresql.Driver
  flyway:
    baselineOnMigrate: true
  security:
    oauth2:
      resourceserver:
        jwt:
          issuer-uri: ${spring.security.provider.url}
      client:
        registration:
          keycloak:
            provider: keycloak
            client-name: ${spring.security.client.id}
            client-id: ${spring.security.client.id}
            client-secret: ${spring.security.client.secret}
            scope: openid,offline_access,profile
            authorization-grant-type: authorization_code

        provider:
          keycloak:
            issuer-uri: ${spring.security.provider.url}
            user-name-attribute: preferred_username
server:
  port: ${SERVER_PORT}

logging:
  level:
    org:
      springframework:
        web:
          filter:
            CommonsRequestLoggingFilter: DEBUG
