create table glider_status_logs (id integer generated by default as identity, created_at timestamp(6), updated_at timestamp(6), glider_name varchar(255), note varchar(255), user_email varchar(255), new_glider_status_id integer, old_glider_status_id integer, primary key (id));
alter table if exists glider_status_logs add constraint FK3lfy1ok11ggv4108gcqy5vqwt foreign key (new_glider_status_id) references glider_status;
alter table if exists glider_status_logs add constraint FKca29phc0n3id3jjabh36ds3s7 foreign key (old_glider_status_id) references glider_status;
