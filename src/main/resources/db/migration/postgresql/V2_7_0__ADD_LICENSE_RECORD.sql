create table license_gcs_model (id integer generated by default as identity, license_record_id integer, gcs_model varchar(255), primary key (id));
create table license_mailbox_model (id integer generated by default as identity, license_record_id integer, mailbox_model varchar(255), primary key (id));
create table license_qualification (id integer generated by default as identity, license_record_id integer, qualification varchar(255), primary key (id));
create table license_record (id integer generated by default as identity, student_email varchar(255), instructor_email varchar(255), instructor_name varchar(255), license_date date, license_duration integer, license_expiry_date date, license_status varchar(255), license_type varchar(255), student_name varchar(255), upgrade_to_service_crew_date date, primary key (id));
create table license_ua_model (id integer generated by default as identity, license_record_id integer, ua_model varchar(255), primary key (id));
