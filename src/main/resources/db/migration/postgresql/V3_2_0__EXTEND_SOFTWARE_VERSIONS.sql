alter table if exists glider add column autopilot_software_version_id integer;
alter table if exists glider add column desired_autopilot_software_version_id integer;
alter table if exists glider add column desired_fts_pixhawk_software_version_id integer;
alter table if exists glider add column desired_fts_raspi_software_version_id integer;
alter table if exists glider add column desired_jetson_software_version_id integer;
alter table if exists glider add column fts_pixhawk_software_version_id integer;
alter table if exists glider add column fts_raspi_software_version_id integer;
alter table if exists glider add column jetson_software_version_id integer;
create table software_version (id integer generated by default as identity, created_at timestamp(6), updated_at timestamp(6), name varchar(255), type varchar(255), primary key (id));
alter table if exists glider add constraint FKjh06snreil074fha92ixvyavd foreign key (autopilot_software_version_id) references software_version;
alter table if exists glider add constraint FKpp88kesbrhl9bs86jsbh5jsun foreign key (desired_autopilot_software_version_id) references software_version;
alter table if exists glider add constraint FKfilk7ujxqteq959scm7xrwyoc foreign key (desired_fts_pixhawk_software_version_id) references software_version;
alter table if exists glider add constraint FKehrftpotrxnfvvtalf4f2ws5c foreign key (desired_fts_raspi_software_version_id) references software_version;
alter table if exists glider add constraint FKgn6xex0ox5q7xv7l3hb7hci4c foreign key (desired_jetson_software_version_id) references software_version;
alter table if exists glider add constraint FK6v7dhjfps3omhdsx5x56t7ojf foreign key (fts_pixhawk_software_version_id) references software_version;
alter table if exists glider add constraint FKfgy6ib36cr4rl4w20sm7r1rih foreign key (fts_raspi_software_version_id) references software_version;
alter table if exists glider add constraint FKhst5mqspgahhs81mj8obvbm3t foreign key (jetson_software_version_id) references software_version;
