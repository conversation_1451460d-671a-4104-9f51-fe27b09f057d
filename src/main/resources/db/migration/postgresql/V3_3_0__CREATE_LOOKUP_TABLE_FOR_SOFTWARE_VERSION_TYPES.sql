alter table if exists software_version add column software_version_type_id integer;
create table software_version_type (id integer generated by default as identity, created_at timestamp(6), updated_at timestamp(6), name varchar(255), primary key (id));
alter table if exists software_version add constraint FKs7v5n2coigw2suwjpoomuai7y foreign key (software_version_type_id) references software_version_type;
