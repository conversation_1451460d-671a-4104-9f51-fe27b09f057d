alter table if exists glider add column manufacturing_date timestamp(6);
alter table if exists glider add column registration_code varchar(255);
alter table if exists glider add column registration_complete boolean;
alter table if exists glider add column status_id integer;
alter table if exists glider add column region_id integer;
create table region (id integer generated by default as identity, created_at timestamp(6), updated_at timestamp(6), country varchar(255), description varchar(255), support_email varchar(255), support_phone_country_code varchar(255), support_phone_number varchar(255), primary key (id));
alter table if exists glider add constraint FKhn9qxul4lg9yvjpvxktc9ca2j foreign key (status_id) references status;
alter table if exists glider add constraint FKj6t24fi1nierrnfaook0hnwff foreign key (region_id) references region;
