create table maintenance (id integer generated by default as identity, completed_date date, due_date date, is_scheduled boolean, notes varchar(65536), glider_id integer, mailbox_id integer, maintenance_type_id integer not null, primary key (id));
alter table if exists maintenance add constraint FKdhmfhx20si9ulwiqsps3ki5xe foreign key (glider_id) references glider;
alter table if exists maintenance add constraint FKjd3rg51hfx75n9pdx1440jq4c foreign key (mailbox_id) references mailbox;
alter table if exists maintenance add constraint FKgqphq56s0igxcc4pvpgy2bg30 foreign key (maintenance_type_id) references maintenance_type;
