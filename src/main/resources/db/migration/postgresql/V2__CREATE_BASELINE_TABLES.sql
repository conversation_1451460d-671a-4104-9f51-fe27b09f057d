create table glider (id integer generated by default as identity, created_at timestamp(6), updated_at timestamp(6), generation varchar(255), line varchar(255), number varchar(255), vpn_ip varchar(255), vpn_network_id varchar(255), glider_mode_id integer, primary key (id));
create table glider_maintenance (id integer generated by default as identity, completed_date date, due_date date, is_scheduled boolean, notes varchar(255), glider_id integer not null, primary key (id));
create table glider_mode (id integer generated by default as identity, created_at timestamp(6), updated_at timestamp(6), description varchar(255), name varchar(255), primary key (id));
create table location (id integer generated by default as identity, created_at timestamp(6), updated_at timestamp(6), gps_alt float(53), gps_lat float(53), gps_long float(53), name varchar(255), picture_url varchar(255), video_url varchar(255), location_category_id integer, location_status_id integer, primary key (id));
create table location_category (id integer generated by default as identity, created_at timestamp(6), updated_at timestamp(6), description varchar(255), name varchar(255), primary key (id));
create table location_status (id integer generated by default as identity, created_at timestamp(6), updated_at timestamp(6), description varchar(255), name varchar(255), primary key (id));
create table status (id integer generated by default as identity, created_at timestamp(6), updated_at timestamp(6), name varchar(255), primary key (id));
alter table if exists glider add constraint FK7pyxpk4rrg8xlghymkslkq41f foreign key (glider_mode_id) references glider_mode;
alter table if exists glider_maintenance add constraint FKhomxmsnfdl3q9ygjhoabp0go6 foreign key (glider_id) references glider;
alter table if exists location add constraint FKstmyesf4to3wjd3vyito3ta1q foreign key (location_category_id) references location_category;
alter table if exists location add constraint FK7k7eggyhv2njhl3kadw62sx5k foreign key (location_status_id) references location_status;
