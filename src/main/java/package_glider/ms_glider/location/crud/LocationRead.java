package package_glider.ms_glider.location.crud;
import java.time.LocalDateTime;
import package_glider.ms_glider.location.model.Location;
import package_glider.ms_glider.location.model.LocationCategory;
import package_glider.ms_glider.location.model.LocationStatus;


public class LocationRead {
    private Integer id;
    private String name;
    private String pictureUrl;
    private String videoUrl;
    private Double latitude;
    private Double longitude;
    private Double radius;
    private LocationCategory locationCategory;
    private LocationStatus locationStatus;
    private LocalDateTime createdAt;
    private LocalDateTime updatedAt;

    public static LocationRead from(final Location item) {
        final LocationRead location = new LocationRead();
        location.setId(item.getId());
        location.setName(item.getName());
        location.setPictureUrl(item.getPictureUrl());
        location.setVideoUrl(item.getVideoUrl());
        location.setLatitude(item.getLatitude());
        location.setLongitude(item.getLongitude());
        location.setRadius(item.getRadius());
        location.setLocationCategory(item.getLocationCategory());
        location.setLocationStatus(item.getLocationStatus());
        location.setCreatedAt(item.getCreatedAt());
        location.setUpdatedAt(item.getUpdatedAt());
        return location;
    }

    public Integer getId() {
        return id;
    }

    public void setId(Integer id) {
        this.id = id;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public String getPictureUrl() {
        return pictureUrl;
    }

    public void setPictureUrl(String pictureUrl) {
        this.pictureUrl = pictureUrl;
    }

    public String getVideoUrl() {
        return videoUrl;
    }

    public void setVideoUrl(String videoUrl) {
        this.videoUrl = videoUrl;
    }

    public LocationCategory getLocationCategory() {
        return locationCategory;
    }

    public void setLocationCategory(LocationCategory locationCategory) {
        this.locationCategory = locationCategory;
    }

    public LocationStatus getLocationStatus() {
        return locationStatus;
    }

    public void setLocationStatus(LocationStatus locationStatus) {
        this.locationStatus = locationStatus;
    }

    public LocalDateTime getCreatedAt() {
        return createdAt;
    }

    public void setCreatedAt(LocalDateTime createdAt) {
        this.createdAt = createdAt;
    }

    public LocalDateTime getUpdatedAt() {
        return updatedAt;
    }

    public void setUpdatedAt(LocalDateTime updatedAt) {
        this.updatedAt = updatedAt;
    }

    public Double getLatitude() {
        return latitude;
    }

    public void setLatitude(Double latitude) {
        this.latitude = latitude;
    }

    public Double getLongitude() {
        return longitude;
    }

    public void setLongitude(Double longitude) {
        this.longitude = longitude;
    }

    public Double getRadius() {
        return radius;
    }

    public void setRadius(Double radius) {
        this.radius = radius;
    }

    
}