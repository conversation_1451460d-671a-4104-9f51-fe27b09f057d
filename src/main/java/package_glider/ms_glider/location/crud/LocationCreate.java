package package_glider.ms_glider.location.crud;

import java.time.LocalDateTime;
import package_glider.ms_glider.location.model.Location;
import package_glider.ms_glider.location.model.LocationCategory;
import package_glider.ms_glider.location.model.LocationStatus;




public class LocationCreate {
    private String name;
    private String pictureUrl;
    private String videoUrl;
    private Double latitude;
    private Double longitude;
    private Double radius;
    private Integer locationCategoryId;
    private Integer locationStatusId;
    private LocalDateTime createdAt;
    private LocalDateTime updatedAt;


    public static Location to(final LocationCreate item, final LocationCategory locationCategory, final LocationStatus locationStatus) {
        final Location res = new Location();
        res.setName(item.getName());
        res.setPictureUrl(item.getPictureUrl());
        res.setVideoUrl(item.getVideoUrl());
        res.setLatitude(item.getLatitude());
        res.setLongitude(item.getLongitude());
        res.setRadius(item.getRadius());
        res.setLocationCategory(locationCategory);
        res.setLocationStatus(locationStatus);
        res.setCreatedAt(item.getCreatedAt());
        res.setUpdatedAt(item.getUpdatedAt());
        
        return res;
    }


    public String getName() {
        return name;
    }


    public void setName(String name) {
        this.name = name;
    }



    public String getPictureUrl() {
        return pictureUrl;
    }


    public void setPictureUrl(String pictureUrl) {
        this.pictureUrl = pictureUrl;
    }


    public String getVideoUrl() {
        return videoUrl;
    }


    public void setVideoUrl(String videoUrl) {
        this.videoUrl = videoUrl;
    }


    public Integer getLocationCategoryId() {
        return locationCategoryId;
    }


    public void setLocationCategoryId(Integer locationCategoryId) {
        this.locationCategoryId = locationCategoryId;
    }


    public Integer getLocationStatusId() {
        return locationStatusId;
    }


    public void setLocationStatusId(Integer locationStatusId) {
        this.locationStatusId = locationStatusId;
    }


    public LocalDateTime getCreatedAt() {
        return createdAt;
    }


    public void setCreatedAt(LocalDateTime createdAt) {
        this.createdAt = createdAt;
    }


    public LocalDateTime getUpdatedAt() {
        return updatedAt;
    }


    public void setUpdatedAt(LocalDateTime updatedAt) {
        this.updatedAt = updatedAt;
    }


    public Double getLatitude() {
        return latitude;
    }


    public void setLatitude(Double latitude) {
        this.latitude = latitude;
    }


    public Double getLongitude() {
        return longitude;
    }


    public void setLongitude(Double longitude) {
        this.longitude = longitude;
    }


    public Double getRadius() {
        return radius;
    }


    public void setRadius(Double radius) {
        this.radius = radius;
    }    

    
}