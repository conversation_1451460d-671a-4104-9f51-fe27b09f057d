package package_glider.ms_glider.location.crud;
import java.time.LocalDateTime;
import package_glider.ms_glider.location.model.LocationStatus;

public class LocationStatusRead {
    private Integer id;
    private String name;
    private String description;
    private LocalDateTime createdAt;
    private LocalDateTime updatedAt;

    public static LocationStatusRead from(final LocationStatus item) {
        final LocationStatusRead locationStatus = new LocationStatusRead();
        locationStatus.setId(item.getId());
        locationStatus.setName(item.getName());
        locationStatus.setDescription(item.getDescription());
        locationStatus.setCreatedAt(item.getCreatedAt());
        locationStatus.setUpdatedAt(item.getUpdatedAt());
        return locationStatus;
    }

    public Integer getId() {
        return id;
    }

    public void setId(Integer id) {
        this.id = id;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public String getDescription() {
        return description;
    }

    public void setDescription(String description) {
        this.description = description;
    }

    public LocalDateTime getCreatedAt() {
        return createdAt;
    }

    public void setCreatedAt(LocalDateTime createdAt) {
        this.createdAt = createdAt;
    }

    public LocalDateTime getUpdatedAt() {
        return updatedAt;
    }

    public void setUpdatedAt(LocalDateTime updatedAt) {
        this.updatedAt = updatedAt;
    }

    
}
