package package_glider.ms_glider.location.crud;
import java.time.LocalDateTime;
import package_glider.ms_glider.location.model.LocationCategory;

public class LocationCategoryRead {
    private Integer id;
    private String name;
    private String description;
    private LocalDateTime createdAt;
    private LocalDateTime updatedAt;

    public static LocationCategoryRead from(final LocationCategory item) {
        final LocationCategoryRead locationCategory = new LocationCategoryRead();
        locationCategory.setId(item.getId());
        locationCategory.setName(item.getName());
        locationCategory.setDescription(item.getDescription());
        locationCategory.setCreatedAt(item.getCreatedAt());
        locationCategory.setUpdatedAt(item.getUpdatedAt());
        return locationCategory;
    }

    public Integer getId() {
        return id;
    }

    public void setId(Integer id) {
        this.id = id;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public String getDescription() {
        return description;
    }

    public void setDescription(String description) {
        this.description = description;
    }

    public LocalDateTime getCreatedAt() {
        return createdAt;
    }

    public void setCreatedAt(LocalDateTime createdAt) {
        this.createdAt = createdAt;
    }

    public LocalDateTime getUpdatedAt() {
        return updatedAt;
    }

    public void setUpdatedAt(LocalDateTime updatedAt) {
        this.updatedAt = updatedAt;
    }

    
}
