package package_glider.ms_glider.location.model;

import jakarta.persistence.*;
import lombok.EqualsAndHashCode;

@Entity
@Table(name = "location_status")
@EqualsAndHashCode(callSuper = true)
public final class LocationStatus extends Base {
    @Column(name = "name")
    private String name;
    @Column(name = "description")
    private String description;
    public String getName() {
        return name;
    }
    public void setName(String name) {
        this.name = name;
    }
    public String getDescription() {
        return description;
    }
    public void setDescription(String description) {
        this.description = description;
    }
}