package package_glider.ms_glider.location.model;

import jakarta.persistence.*;
import lombok.EqualsAndHashCode;


@Entity
@Table(name = "location")
@EqualsAndHashCode(callSuper = true)
public final class Location extends Base {
    
    @Column(name = "name")
    private String name;
    @Column(name = "gps_alt")
    private Double gpsAlt;
    @Column(name = "picture_url")
    private String pictureUrl;
    @Column(name = "video_url")
    private String videoUrl;
    @Column(name = "latitude")
    private Double latitude;
    @Column(name = "longitude")
    private Double longitude;
    @Column(name = "radius")
    private Double radius;

    @ManyToOne
    @JoinColumn(name = "location_category_id", nullable = true)
    private LocationCategory locationCategory;

    @ManyToOne
    @JoinColumn(name = "location_status_id", nullable = true)
    private LocationStatus locationStatus;

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public Double getGpsAlt() {
        return gpsAlt;
    }

    public void setGpsAlt(Double gpsAlt) {
        this.gpsAlt = gpsAlt;
    }

    public String getPictureUrl() {
        return pictureUrl;
    }

    public void setPictureUrl(String pictureUrl) {
        this.pictureUrl = pictureUrl;
    }

    public String getVideoUrl() {
        return videoUrl;
    }

    public void setVideoUrl(String videoUrl) {
        this.videoUrl = videoUrl;
    }

    public LocationCategory getLocationCategory() {
        return locationCategory;
    }

    public void setLocationCategory(LocationCategory locationCategory) {
        this.locationCategory = locationCategory;
    }

    public LocationStatus getLocationStatus() {
        return locationStatus;
    }

    public void setLocationStatus(LocationStatus locationStatus) {
        this.locationStatus = locationStatus;
    }

    public Double getLatitude() {
        return latitude;
    }

    public void setLatitude(Double latitude) {
        this.latitude = latitude;
    }

    public Double getLongitude() {
        return longitude;
    }

    public void setLongitude(Double longitude) {
        this.longitude = longitude;
    }

    public Double getRadius() {
        return radius;
    }

    public void setRadius(Double radius) {
        this.radius = radius;
    }

    
}