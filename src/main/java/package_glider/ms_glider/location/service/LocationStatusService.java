package package_glider.ms_glider.location.service;

import java.util.Optional;
import java.util.stream.Stream;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Component;
import package_glider.ms_glider.location.model.LocationStatus;
import package_glider.ms_glider.location.repository.LocationStatusRepository;



@Component
@RequiredArgsConstructor
public final class LocationStatusService {
    private final LocationStatusRepository locationStatusRepository;

    public Optional<LocationStatus> findById(final Integer id) {
        return id == null ? Optional.empty() : locationStatusRepository.findById(id);
    }

    public Stream<LocationStatus> list() {
        return locationStatusRepository.findAll().stream();
    }

    public Optional<LocationStatus> add(final LocationStatus locationStatus) {
        return Optional.of(locationStatusRepository.save(locationStatus));
    }
}