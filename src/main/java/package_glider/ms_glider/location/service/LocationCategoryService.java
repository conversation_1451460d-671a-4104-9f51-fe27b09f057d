package package_glider.ms_glider.location.service;

import java.util.Optional;
import java.util.stream.Stream;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Component;
import package_glider.ms_glider.location.model.LocationCategory;
import package_glider.ms_glider.location.repository.LocationCategoryRepository;




@Component
@RequiredArgsConstructor
public final class LocationCategoryService {
    private final LocationCategoryRepository locationCategoryRepository;

    public Optional<LocationCategory> findById(final Integer id) {
        return id == null ? Optional.empty() : locationCategoryRepository.findById(id);
    }

    public Stream<LocationCategory> list() {
        return locationCategoryRepository.findAll().stream();
    }

    public Optional<LocationCategory> add(final LocationCategory locationCategory) {
        return Optional.of(locationCategoryRepository.save(locationCategory));
    }
}