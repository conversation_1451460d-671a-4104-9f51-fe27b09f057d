package package_glider.ms_glider.location.service;

import java.util.Optional;
import java.util.stream.Stream;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Component;
import package_glider.ms_glider.location.model.Location;
import package_glider.ms_glider.location.repository.LocationRepository;




@Component
@RequiredArgsConstructor
public final class LocationService {
    private final LocationRepository locationRepository;


    public Optional<Location> findById(final Integer id) {
        return locationRepository.findById(id);
    }

    public Stream<Location> list() {
        return locationRepository.findAll().stream();
    }

    public Optional<Location> add(final Location location) {
        final Location savedLocation = locationRepository.save(location);
        return Optional.of(savedLocation);
    }
}