package package_glider.ms_glider.location.controller;

import io.swagger.v3.oas.annotations.Operation;
import java.util.stream.Stream;
import lombok.RequiredArgsConstructor;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.CrossOrigin;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import package_glider.ms_glider.location.crud.LocationCategoryCreate;
import package_glider.ms_glider.location.crud.LocationCategoryRead;
import package_glider.ms_glider.location.service.LocationCategoryService;



@RestController
@RequestMapping("/api/location-categories")
@CrossOrigin("*")
@RequiredArgsConstructor
public class LocationCategoryController {
    private final LocationCategoryService locationCategoryService;

    @Operation(summary = "List all location categories")
    @GetMapping
    Stream<LocationCategoryRead> all() {
        return locationCategoryService.list().map(item -> LocationCategoryRead.from(item));
    }

    @Operation(summary = "Register a new location category")
    @PostMapping
    LocationCategoryRead add(final @RequestBody @Validated LocationCategoryCreate newLocationCategory) {
        return locationCategoryService.add(LocationCategoryCreate.to(newLocationCategory))
                .map(item -> LocationCategoryRead.from(item)).orElseThrow(() -> new NotFound());
    }
}
