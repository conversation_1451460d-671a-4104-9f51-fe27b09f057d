package package_glider.ms_glider.location.controller;

import io.swagger.v3.oas.annotations.Operation;
import java.util.stream.Stream;
import lombok.RequiredArgsConstructor;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.CrossOrigin;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import package_glider.ms_glider.location.crud.LocationStatusCreate;
import package_glider.ms_glider.location.crud.LocationStatusRead;
import package_glider.ms_glider.location.service.LocationStatusService;


@RestController
@RequestMapping("/api/location-statuses")
@CrossOrigin("*")
@RequiredArgsConstructor
public class LocationStatusController {
    private final LocationStatusService locationStatusService;

    @Operation(summary = "List all location statuses")
    @GetMapping
    Stream<LocationStatusRead> all() {
        return locationStatusService.list().map(item -> LocationStatusRead.from(item));
    }

    @Operation(summary = "Register a new location status")
    @PostMapping
    LocationStatusRead add(final @RequestBody @Validated LocationStatusCreate newLocationStatus) {
        return locationStatusService.add(LocationStatusCreate.to(newLocationStatus))
                .map(item -> LocationStatusRead.from(item)).orElseThrow(() -> new NotFound());
    }
}
