package package_glider.ms_glider.location.controller;

import io.swagger.v3.oas.annotations.Operation;
import java.util.stream.Stream;
import lombok.RequiredArgsConstructor;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.CrossOrigin;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import package_glider.ms_glider.location.crud.LocationCreate;
import package_glider.ms_glider.location.crud.LocationRead;
import package_glider.ms_glider.location.model.LocationCategory;
import package_glider.ms_glider.location.model.LocationStatus;
import package_glider.ms_glider.location.service.LocationCategoryService;
import package_glider.ms_glider.location.service.LocationService;
import package_glider.ms_glider.location.service.LocationStatusService;




class NotFound extends RuntimeException {
}

@RestController
@RequestMapping("/api/locations")
@CrossOrigin("*")
@RequiredArgsConstructor
public class LocationController {
    private final LocationService locationService;
    private final LocationCategoryService locationCategoryService;
    private final LocationStatusService locationStatusService;

    
    @Operation(summary = "Get a location by id")
    @GetMapping("/{id}")
    LocationRead result(final @PathVariable Integer id) {
        return locationService.findById(id).map(item -> LocationRead.from(item)).orElseThrow(() -> new NotFound());
    }

    @Operation(summary = "List all locations")
    @GetMapping
    Stream<LocationRead> all() {
        return locationService.list().map(item -> LocationRead.from(item));
    }

    @Operation(summary = "Register a new location")
    @PostMapping
    LocationRead add(final @RequestBody @Validated LocationCreate newLocation) {
        return locationService.add(LocationCreate.to(newLocation, locationCategory(newLocation.getLocationCategoryId()), locationStatus(newLocation.getLocationStatusId())))
                .map(item -> LocationRead.from(item)).orElseThrow(() -> new NotFound());
    }


    private LocationCategory locationCategory(final Integer locationCategoryId) {
        return locationCategoryService.findById(locationCategoryId).orElse(null);
    }

    private LocationStatus locationStatus(final Integer locationStatusId) {
        return locationStatusService.findById(locationStatusId).orElse(null);
    }
}