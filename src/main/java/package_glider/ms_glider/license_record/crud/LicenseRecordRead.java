package package_glider.ms_glider.license_record.crud;
import package_glider.ms_glider.license_record.model.LicenseRecord;


public class LicenseRecordRead {
    private String studentEmail;
    private String studentName;
    private String instructorEmail;
    private String instructorName;
    private String licenseType;
    private String licenseDate;
    private String licenseExpiryDate;
    private String licenseDuration;
    private String licenseStatus;
    private String upgradeToServiceCrewDate;
    private String uaModel;
    private String gcsModel;
    private String mailboxModel;
    private String qualifications;


    public static LicenseRecordRead from(final LicenseRecord item) {
        final LicenseRecordRead licenseRecord = new LicenseRecordRead();

        licenseRecord.setStudentEmail(item.getStudentEmail());
        licenseRecord.setStudentName(item.getStudentName());
        licenseRecord.setInstructorEmail(item.getInstructorEmail());
        licenseRecord.setInstructorName(item.getInstructorName());
        licenseRecord.setLicenseType(item.getLicenseType());
        licenseRecord.setLicenseDate(item.getLicenseDate());
        licenseRecord.setLicenseExpiryDate(item.getLicenseExpiryDate());
        licenseRecord.setLicenseDuration(item.getLicenseDuration());
        licenseRecord.setLicenseStatus(item.getLicenseStatus());
        licenseRecord.setUpgradeToServiceCrewDate(item.getUpgradeToServiceCrewDate());
        licenseRecord.setUaModel(item.getUaModel());
        licenseRecord.setGcsModel(item.getGcsModel());
        licenseRecord.setMailboxModel(item.getMailboxModel());
        licenseRecord.setQualifications(item.getQualifications());
        
        return licenseRecord;
    }

    public String getStudentEmail() {
        return studentEmail;
    }

    public void setStudentEmail(String studentEmail) {
        this.studentEmail = studentEmail;
    }

    public String getStudentName() {
        return studentName;
    }

    public void setStudentName(String studentName) {
        this.studentName = studentName;
    }

    public String getInstructorEmail() {
        return instructorEmail;
    }

    public void setInstructorEmail(String instructorEmail) {
        this.instructorEmail = instructorEmail;
    }

    public String getInstructorName() {
        return instructorName;
    }

    public void setInstructorName(String instructorName) {
        this.instructorName = instructorName;
    }

    public String getLicenseType() {
        return licenseType;
    }

    public void setLicenseType(String licenseType) {
        this.licenseType = licenseType;
    }

    public String getLicenseDate() {
        return licenseDate;
    }

    public void setLicenseDate(String licenseDate) {
        this.licenseDate = licenseDate;
    }

    public String getLicenseExpiryDate() {
        return licenseExpiryDate;
    }

    public void setLicenseExpiryDate(String licenseExpiryDate) {
        this.licenseExpiryDate = licenseExpiryDate;
    }

    public String getLicenseDuration() {
        return licenseDuration;
    }

    public void setLicenseDuration(String licenseDuration) {
        this.licenseDuration = licenseDuration;
    }

    public String getLicenseStatus() {
        return licenseStatus;
    }

    public void setLicenseStatus(String licenseStatus) {
        this.licenseStatus = licenseStatus;
    }

    public String getUpgradeToServiceCrewDate() {
        return upgradeToServiceCrewDate;
    }

    public void setUpgradeToServiceCrewDate(String upgradeToServiceCrewDate) {
        this.upgradeToServiceCrewDate = upgradeToServiceCrewDate;
    }

    public String getUaModel() {
        return uaModel;
    }

    public void setUaModel(String uaModel) {
        this.uaModel = uaModel;
    }

    public String getGcsModel() {
        return gcsModel;
    }

    public void setGcsModel(String gcsModel) {
        this.gcsModel = gcsModel;
    }

    public String getMailboxModel() {
        return mailboxModel;
    }

    public void setMailboxModel(String mailboxModel) {
        this.mailboxModel = mailboxModel;
    }

    public String getQualifications() {
        return qualifications;
    }

    public void setQualifications(String qualifications) {
        this.qualifications = qualifications;
    }

    

    
}