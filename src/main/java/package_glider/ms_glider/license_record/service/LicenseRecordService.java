package package_glider.ms_glider.license_record.service;

import java.util.List;
import java.util.Optional;
import java.util.stream.Collectors;
import java.util.stream.Stream;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;

import package_glider.ms_glider.license_record.repository.LicenseRecordRepository;
import package_glider.ms_glider.license_record.model.LicenseRecord;
import package_glider.ms_glider.license_record.crud.LicenseRecordRead;


@Component
@RequiredArgsConstructor
public class LicenseRecordService {
    private final LicenseRecordRepository licenseRecordRepository;


    public Optional<LicenseRecord> findById(final Integer id) {
        return licenseRecordRepository.findById(id);
    }

    @Transactional(readOnly = true)
    public List<LicenseRecordRead> findAllByStudentEmail(final String studentEmail) {
        return licenseRecordRepository.findByStudentEmail(studentEmail)
        .map(LicenseRecordRead::from)
        .collect(Collectors.toList());
    }

    public Stream<LicenseRecord> list() {
        return licenseRecordRepository.findAll().stream();
    }

    public Optional<LicenseRecord> add(final LicenseRecord licenseRecord) {
        final LicenseRecord newLicenseRecord = licenseRecordRepository.save(licenseRecord);
        return Optional.of(newLicenseRecord);
    }
}