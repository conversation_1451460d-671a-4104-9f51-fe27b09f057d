package package_glider.ms_glider.license_record.controller;

import io.swagger.v3.oas.annotations.Operation;
import java.util.List;
import java.util.stream.Stream;
import lombok.RequiredArgsConstructor;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.CrossOrigin;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import package_glider.ms_glider.license_record.service.LicenseRecordService;
import package_glider.ms_glider.license_record.crud.LicenseRecordCreate;
import package_glider.ms_glider.license_record.crud.LicenseRecordRead;





class NotFound extends RuntimeException {
}

@RestController
@RequestMapping("/api/license-records")
@CrossOrigin("*")
@RequiredArgsConstructor
public class LicenseRecordController {
    private final LicenseRecordService licenseRecordService;

    
    @Operation(summary = "Get a license record by id")
    @GetMapping("/{id}")
    LicenseRecordRead result(final @PathVariable Integer id) {
        return licenseRecordService.findById(id).map(item -> LicenseRecordRead.from(item)).orElseThrow(() -> new NotFound());
    }
    
    @Operation(summary = "Get license records by student email")
    @GetMapping("/student-email/{studentEmail}")
    List<LicenseRecordRead> resultByGliderId(final @PathVariable String studentEmail) {
        return licenseRecordService.findAllByStudentEmail(studentEmail);
    }

    @Operation(summary = "List all license records history")
    @GetMapping
    Stream<LicenseRecordRead> all() {
        return licenseRecordService.list().map(item -> LicenseRecordRead.from(item));
    }

    @Operation(summary = "Register a new license record")
    @PostMapping
    LicenseRecordRead add(final @RequestBody @Validated LicenseRecordCreate newLicenseRecord) {
        LicenseRecordRead res =  licenseRecordService.add(LicenseRecordCreate.to(newLicenseRecord)).map(LicenseRecordRead::from).orElseThrow(NotFound::new);
        return res;
    }
}