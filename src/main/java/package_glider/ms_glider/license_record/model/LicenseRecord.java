package package_glider.ms_glider.license_record.model;

import jakarta.persistence.*;
import lombok.EqualsAndHashCode;

@Entity
@Table(name = "license_record")
@EqualsAndHashCode(callSuper = true)
public final class LicenseRecord extends Base {
    
    @Column(name = "student_email")
    private String studentEmail;

    @Column(name = "student_name")
    private String studentName;

    @Column(name = "instructor_email")
    private String instructorEmail;

    @Column(name = "instructor_name")
    private String instructorName;

    @Column(name = "license_type", nullable = true)
    private String licenseType;

    @Column(name = "license_date", nullable = true)
    private String licenseDate;

    @Column(name = "license_expiry_date", nullable = true)
    private String licenseExpiryDate;
    
    @Column(name = "license_duration", nullable = true)
    private String licenseDuration;

    @Column(name = "license_status", nullable = true)
    private String licenseStatus;

    @Column(name = "upgrade_to_service_crew_date", nullable = true)
    private String upgradeToServiceCrewDate;

    @Column(name = "ua_model", nullable = true)
    private String uaModel;

    @Column(name = "gcs_model", nullable = true)
    private String gcsModel;

    @Column(name = "mailbox_model", nullable = true)
    private String mailboxModel;

    @Column(name = "qualifications", nullable = true)
    private String qualifications;

    public String getStudentEmail() {
        return studentEmail;
    }

    public void setStudentEmail(String studentEmail) {
        this.studentEmail = studentEmail;
    }

    public String getStudentName() {
        return studentName;
    }

    public void setStudentName(String studentName) {
        this.studentName = studentName;
    }

    public String getInstructorEmail() {
        return instructorEmail;
    }

    public void setInstructorEmail(String instructorEmail) {
        this.instructorEmail = instructorEmail;
    }

    public String getInstructorName() {
        return instructorName;
    }

    public void setInstructorName(String instructorName) {
        this.instructorName = instructorName;
    }

    public String getLicenseType() {
        return licenseType;
    }

    public void setLicenseType(String licenseType) {
        this.licenseType = licenseType;
    }

    public String getLicenseDate() {
        return licenseDate;
    }

    public void setLicenseDate(String licenseDate) {
        this.licenseDate = licenseDate;
    }

    public String getLicenseExpiryDate() {
        return licenseExpiryDate;
    }

    public void setLicenseExpiryDate(String licenseExpiryDate) {
        this.licenseExpiryDate = licenseExpiryDate;
    }

    public String getLicenseDuration() {
        return licenseDuration;
    }

    public void setLicenseDuration(String licenseDuration) {
        this.licenseDuration = licenseDuration;
    }

    public String getLicenseStatus() {
        return licenseStatus;
    }

    public void setLicenseStatus(String licenseStatus) {
        this.licenseStatus = licenseStatus;
    }

    public String getUpgradeToServiceCrewDate() {
        return upgradeToServiceCrewDate;
    }

    public void setUpgradeToServiceCrewDate(String upgradeToServiceCrewDate) {
        this.upgradeToServiceCrewDate = upgradeToServiceCrewDate;
    }

    public String getUaModel() {
        return uaModel;
    }

    public void setUaModel(String uaModel) {
        this.uaModel = uaModel;
    }

    public String getGcsModel() {
        return gcsModel;
    }

    public void setGcsModel(String gcsModel) {
        this.gcsModel = gcsModel;
    }

    public String getMailboxModel() {
        return mailboxModel;
    }

    public void setMailboxModel(String mailboxModel) {
        this.mailboxModel = mailboxModel;
    }

    public String getQualifications() {
        return qualifications;
    }

    public void setQualifications(String qualifications) {
        this.qualifications = qualifications;
    }

    
    

}