package package_glider.ms_glider.live_glider_data.service;

import java.util.Optional;
import java.util.stream.Stream;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Component;
import package_glider.ms_glider.live_glider_data.model.GliderStatus;
import package_glider.ms_glider.live_glider_data.repository.GliderStatusRepository;


@Component
@RequiredArgsConstructor
public final class GliderStatusService {
    private final GliderStatusRepository StatusRepository;

    public Optional<GliderStatus> findById(final Integer id) {
        return id == null ? Optional.empty() : StatusRepository.findById(id);
    }

    public Optional<GliderStatus> findByName(final String statusName) {
        return statusName == null ? Optional.empty() : StatusRepository.findByName(statusName);
    }

    public Stream<GliderStatus> list() {
        return StatusRepository.findAll().stream();
    }

    public Optional<GliderStatus> add(final GliderStatus gliderStatus) {
        return Optional.of(StatusRepository.save(gliderStatus));
    }
}