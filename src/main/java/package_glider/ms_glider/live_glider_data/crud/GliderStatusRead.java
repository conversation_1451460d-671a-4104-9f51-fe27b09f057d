package package_glider.ms_glider.live_glider_data.crud;
import java.time.LocalDateTime;
import package_glider.ms_glider.live_glider_data.model.GliderStatus;

public class GliderStatusRead {
    private Integer id;
    private String name;
    private String colorHexcode;
    private LocalDateTime createdAt;
    private LocalDateTime updatedAt;

    public static GliderStatusRead from(final GliderStatus item) {
        final GliderStatusRead gliderStatus = new GliderStatusRead();
        gliderStatus.setId(item.getId());
        gliderStatus.setName(item.getName());
        gliderStatus.setColorHexcode(item.getColorHexcode());
        gliderStatus.setCreatedAt(item.getCreatedAt());
        gliderStatus.setUpdatedAt(item.getUpdatedAt());
        return gliderStatus;
    }

    public Integer getId() {
        return id;
    }

    public void setId(Integer id) {
        this.id = id;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public String getColorHexcode() {
        return colorHexcode;
    }

    public void setColorHexcode(String colorHexcode) {
        this.colorHexcode = colorHexcode;
    }

    public LocalDateTime getCreatedAt() {
        return createdAt;
    }

    public void setCreatedAt(LocalDateTime createdAt) {
        this.createdAt = createdAt;
    }

    public LocalDateTime getUpdatedAt() {
        return updatedAt;
    }

    public void setUpdatedAt(LocalDateTime updatedAt) {
        this.updatedAt = updatedAt;
    }

    
}
