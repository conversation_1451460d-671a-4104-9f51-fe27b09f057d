package package_glider.ms_glider.live_glider_data.crud;

import java.time.LocalDateTime;
import package_glider.ms_glider.live_glider_data.model.GliderStatus;

public class GliderStatusCreate {
    private String name;
    private String colorHexcode;
    private LocalDateTime createdAt;
    private LocalDateTime updatedAt;

    public static GliderStatus to(final GliderStatusCreate item) {
        final GliderStatus res = new GliderStatus();
        res.setName(item.getName());
        res.setColorHexcode(item.getColorHexcode());
        res.setCreatedAt(item.getCreatedAt());
        res.setUpdatedAt(item.getUpdatedAt());

        return res;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public String getColorHexcode() {
        return colorHexcode;
    }

    public void setColorHexcode(String colorHexcode) {
        this.colorHexcode = colorHexcode;
    }
    
    public LocalDateTime getCreatedAt() {
        return createdAt;
    }

    public void setCreatedAt(LocalDateTime createdAt) {
        this.createdAt = createdAt;
    }

    public LocalDateTime getUpdatedAt() {
        return updatedAt;
    }

    public void setUpdatedAt(LocalDateTime updatedAt) {
        this.updatedAt = updatedAt;
    }    
}
