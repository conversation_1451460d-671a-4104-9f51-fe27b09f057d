package package_glider.ms_glider.live_glider_data.controller;

import java.util.List;
import io.swagger.v3.oas.annotations.Operation;
import java.util.stream.Stream;
import lombok.RequiredArgsConstructor;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.CrossOrigin;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import package_glider.ms_glider.live_glider_data.crud.GliderStatusCreate;
import package_glider.ms_glider.live_glider_data.crud.GliderStatusRead;
import package_glider.ms_glider.live_glider_data.service.GliderStatusService;


class NotFound extends RuntimeException {
}

class InvalidStatus extends RuntimeException {
}


@RestController
@RequestMapping("/api/glider-statuses")
@CrossOrigin("*")
@RequiredArgsConstructor
public class GliderStatusController {
    private final GliderStatusService statusService;

    @Operation(summary = "List all glider statuses")
    @GetMapping
    Stream<GliderStatusRead> all() {
        return statusService.list().map(item ->GliderStatusRead.from(item));
    }

    @Operation(summary = "Register a new glider status")
    @PostMapping
    GliderStatusRead add(final @RequestBody @Validated GliderStatusCreate gliderStatus) {
        List<GliderStatusRead> existing_statuses = statusService.list().map(item ->GliderStatusRead.from(item)).toList();
        for (GliderStatusRead status : existing_statuses) {
            if (status.getName().equals(gliderStatus.getName())) {
                throw new InvalidStatus();
            }
        }
        return statusService.add(GliderStatusCreate.to(gliderStatus))
                .map(item -> GliderStatusRead.from(item)).orElseThrow(() -> new NotFound());
    }
}
