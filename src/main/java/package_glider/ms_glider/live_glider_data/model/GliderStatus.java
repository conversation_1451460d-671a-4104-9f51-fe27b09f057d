package package_glider.ms_glider.live_glider_data.model;

import jakarta.persistence.*;
import lombok.EqualsAndHashCode;

@Entity
@Table(name = "glider_status")
@EqualsAndHashCode(callSuper = true)
public final class GliderStatus extends Base {
    @Column(name = "name", unique = true, nullable = false)
    private String name;

    @Column(name = "color_hexcode", nullable = false)
    private String colorHexcode = "#FFFFFF";

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public String getColorHexcode() {
        return colorHexcode;
    }

    public void setColorHexcode(String colorHexcode) {
        this.colorHexcode = colorHexcode;
    }
}