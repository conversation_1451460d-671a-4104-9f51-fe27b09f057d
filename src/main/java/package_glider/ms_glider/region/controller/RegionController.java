package package_glider.ms_glider.region.controller;

import io.swagger.v3.oas.annotations.Operation;
import java.util.stream.Stream;
import lombok.RequiredArgsConstructor;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.CrossOrigin;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import package_glider.ms_glider.region.crud.RegionCreate;
import package_glider.ms_glider.region.crud.RegionRead;
import package_glider.ms_glider.region.service.RegionService;




class NotFound extends RuntimeException {
}


@RestController
@RequestMapping("/api/regions")
@CrossOrigin("*")
@RequiredArgsConstructor
public class RegionController {
    private final RegionService regionService;

    @Operation(summary = "List all regions")
    @GetMapping
    Stream<RegionRead> all() {
        return regionService.list().map(item -> RegionRead.from(item));
    }

    @Operation(summary = "Register a new region")
    @PostMapping
    RegionRead add(final @RequestBody @Validated RegionCreate region) {
        return regionService.add(RegionCreate.to(region))
                .map(item -> RegionRead.from(item)).orElseThrow(() -> new NotFound());
    }
}
