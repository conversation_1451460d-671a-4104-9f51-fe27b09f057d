package package_glider.ms_glider.region.model;

import jakarta.persistence.*;
import lombok.EqualsAndHashCode;

@Entity
@Table(name = "region")
@EqualsAndHashCode(callSuper = true)
public final class Region extends Base {
    @Column(name = "country")
    private String country;

    @Column(name = "description")
    private String description;

    @Column(name = "support_phone_country_code")
    private String supportPhoneCountryCode;

    @Column(name = "support_phone_number")
    private String supportPhoneNumber;

    @Column(name = "support_email")
    private String supportEmail;

    public String getDescription() {
        return description;
    }

    public void setDescription(String description) {
        this.description = description;
    }

    public String getCountry() {
        return country;
    }

    public void setCountry(String country) {
        this.country = country;
    }

    public String getSupportPhoneCountryCode() {
        return supportPhoneCountryCode;
    }

    public void setSupportPhoneCountryCode(String supportPhoneCountryCode) {
        this.supportPhoneCountryCode = supportPhoneCountryCode;
    }

    public String getSupportPhoneNumber() {
        return supportPhoneNumber;
    }

    public void setSupportPhoneNumber(String supportPhoneNumber) {
        this.supportPhoneNumber = supportPhoneNumber;
    }

    public String getSupportEmail() {
        return supportEmail;
    }

    public void setSupportEmail(String supportEmail) {
        this.supportEmail = supportEmail;
    }
    
    
}