package package_glider.ms_glider.region.service;
import java.util.Optional;
import java.util.stream.Stream;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Component;
import package_glider.ms_glider.region.model.Region;
import package_glider.ms_glider.region.repository.RegionRepository;




@Component
@RequiredArgsConstructor
public final class RegionService {
    private final RegionRepository regionRepository;

    public Optional<Region> findById(final Integer id) {
        return id == null ? Optional.empty() : regionRepository.findById(id);
    }

    public Stream<Region> list() {
        return regionRepository.findAll().stream();
    }

    public Optional<Region> add(final Region region) {
        return Optional.of(regionRepository.save(region));
    }
}