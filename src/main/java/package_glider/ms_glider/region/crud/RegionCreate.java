package package_glider.ms_glider.region.crud;
import java.time.LocalDateTime;
import package_glider.ms_glider.region.model.Region;

public class RegionCreate {
    private String country;
    private String description;
    private String supportPhoneCountryCode;
    private String supportPhoneNumber;
    private String supportEmail;
    private LocalDateTime createdAt;
    private LocalDateTime updatedAt;

    public static Region to(final RegionCreate item) {
        final Region res = new Region();
        res.setCountry(item.getCountry());
        res.setDescription(item.getDescription());
        res.setSupportPhoneCountryCode(item.getSupportPhoneCountryCode());
        res.setSupportPhoneNumber(item.getSupportPhoneNumber());
        res.setSupportEmail(item.getSupportEmail());
        res.setCreatedAt(item.getCreatedAt());
        res.setUpdatedAt(item.getUpdatedAt());

        return res;
    }

    public String getDescription() {
        return description;
    }

    public void setDescription(String description) {
        this.description = description;
    }

    public LocalDateTime getCreatedAt() {
        return createdAt;
    }

    public void setCreatedAt(LocalDateTime createdAt) {
        this.createdAt = createdAt;
    }

    public LocalDateTime getUpdatedAt() {
        return updatedAt;
    }

    public void setUpdatedAt(LocalDateTime updatedAt) {
        this.updatedAt = updatedAt;
    }

    public String getCountry() {
        return country;
    }

    public void setCountry(String country) {
        this.country = country;
    }

    public String getSupportPhoneCountryCode() {
        return supportPhoneCountryCode;
    }

    public void setSupportPhoneCountryCode(String supportPhoneCountryCode) {
        this.supportPhoneCountryCode = supportPhoneCountryCode;
    }

    public String getSupportPhoneNumber() {
        return supportPhoneNumber;
    }

    public void setSupportPhoneNumber(String supportPhoneNumber) {
        this.supportPhoneNumber = supportPhoneNumber;
    }

    public String getSupportEmail() {
        return supportEmail;
    }

    public void setSupportEmail(String supportEmail) {
        this.supportEmail = supportEmail;
    }

    
}
