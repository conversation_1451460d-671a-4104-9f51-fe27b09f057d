package package_glider.ms_glider.mailbox.controller;

import io.swagger.v3.oas.annotations.Operation;
import java.util.stream.Stream;
import lombok.RequiredArgsConstructor;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.CrossOrigin;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import package_glider.ms_glider.mailbox.crud.MailboxCreate;
import package_glider.ms_glider.mailbox.crud.MailboxRead;
import package_glider.ms_glider.mailbox.service.MailboxService;




class NotFound extends RuntimeException {
}


@RestController
@RequestMapping("/api/mailboxes")
@CrossOrigin("*")
@RequiredArgsConstructor
public class MailboxController {
    private final MailboxService mailboxService;

    @Operation(summary = "List all mailboxes")
    @GetMapping
    Stream<MailboxRead> all() {
        return mailboxService.list().map(item -> MailboxRead.from(item));
    }

    @Operation(summary = "Register a new mailbox")
    @PostMapping
    MailboxRead add(final @RequestBody @Validated MailboxCreate newMailbox) {
        return mailboxService.add(MailboxCreate.to(newMailbox))
                .map(item -> MailboxRead.from(item)).orElseThrow(() -> new NotFound());
    }
}
