package package_glider.ms_glider.mailbox.service;
import java.util.Optional;
import java.util.stream.Stream;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Component;
import package_glider.ms_glider.mailbox.model.Mailbox;
import package_glider.ms_glider.mailbox.repository.MailboxRepository;




@Component
@RequiredArgsConstructor
public final class MailboxService {
    private final MailboxRepository mailboxRepository;

    public Optional<Mailbox> findById(final Integer id) {
        return id == null ? Optional.empty() : mailboxRepository.findById(id);
    }

    public Stream<Mailbox> list() {
        return mailboxRepository.findAll().stream();
    }

    public Optional<Mailbox> add(final Mailbox region) {
        return Optional.of(mailboxRepository.save(region));
    }
}