package package_glider.ms_glider.mailbox.crud;
import package_glider.ms_glider.mailbox.model.Mailbox;

public class MailboxCreate {
    private String name;

    public static Mailbox to(final MailboxCreate item) {
        final Mailbox res = new Mailbox();
        res.setName(item.getName());

        return res;
    }

    public String getName() {
      return name;
    }

    public void setName(String name) {
      this.name = name;
    }

}
