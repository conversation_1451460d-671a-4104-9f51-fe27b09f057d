package package_glider.ms_glider.mailbox.crud;

import package_glider.ms_glider.mailbox.model.Mailbox;

public class MailboxRead {
    private Integer id;
    private String name;


    public static MailboxRead from(final Mailbox item) {
        final MailboxRead mailbox = new MailboxRead();
        mailbox.setId(item.getId());
        mailbox.setName(item.getName());

        return mailbox;
    }

    public Integer getId() {
        return id;
    }

    public void setId(Integer id) {
        this.id = id;
    }

    public String getName() {
      return name;
    }

    public void setName(String name) {
      this.name = name;
    }

}
