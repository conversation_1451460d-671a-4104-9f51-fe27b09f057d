package package_glider.ms_glider.mailbox.model;

import jakarta.persistence.*;
import lombok.EqualsAndHashCode;

@Entity
@Table(name = "mailbox")
@EqualsAndHashCode(callSuper = true)
public final class Mailbox extends Base {
    @Column(name = "name")
    private String name;

    public String getName() {
      return name;
    }

    public void setName(String name) {
      this.name = name;
    }

}