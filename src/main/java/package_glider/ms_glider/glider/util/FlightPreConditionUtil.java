package package_glider.ms_glider.glider.util;

import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Component;
import package_glider.ms_glider.glider.model.Glider;





@Component
@RequiredArgsConstructor
public final class FlightPreConditionUtil {
    private Boolean isMaintenanceRequiredFlag;
    private String gliderInformationLink;
    private Boolean isAllowedToFly;

    // Improve the naming of the flags
    public Boolean checkMaintenanceRequiredFlag(final Glider glider){
        isMaintenanceRequiredFlag = (glider.getTotalFlightTimeInSeconds()/3600) >= 25; // If the total flight time is more than 25 hours, then maintenance is required
        return isMaintenanceRequiredFlag;
    }

    // Improve the naming of the link
    public String getGliderInformationLink(final Glider glider){
        gliderInformationLink = "https://admin.uphi.ch/fleets/fleet-details/" + glider.getId();
        return gliderInformationLink;
    }

    public Boolean generateIsAllowedToFly(final Glider glider){
        isAllowedToFly = !isMaintenanceRequiredFlag; // The combination of all the flags
        return isAllowedToFly;
    }



    public Boolean getIsMaintenanceRequiredFlag() {
        return isMaintenanceRequiredFlag;
    }

    public void setIsMaintenanceRequiredFlag(Boolean isMaintenanceRequiredFlag) {
        this.isMaintenanceRequiredFlag = isMaintenanceRequiredFlag;
    }

    public String getGliderInformationLink() {
        return gliderInformationLink;
    }

    public void setGliderInformationLink(String gliderInformationLink) {
        this.gliderInformationLink = gliderInformationLink;
    }

    public Boolean getIsAllowedToFly() {
        return isAllowedToFly;
    }

    public void setIsAllowedToFly(Boolean isAllowedToFly) {
        this.isAllowedToFly = isAllowedToFly;
    }

}