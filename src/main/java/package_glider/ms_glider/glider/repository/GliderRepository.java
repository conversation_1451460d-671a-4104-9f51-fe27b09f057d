package package_glider.ms_glider.glider.repository;

import java.util.Optional;
import org.springframework.data.jpa.repository.JpaRepository;
import package_glider.ms_glider.glider.model.Glider;

public interface GliderRepository extends JpaRepository<Glider, Integer> {
    Optional<Glider> findByVpnIp(String vpnIp);

    Optional<Glider> findByPixhawkUuid(String pixhawkUuid);

    Optional<Glider> findByRegistrationCode(String registrationCode);

    Optional<Glider> findByName(String name);

}