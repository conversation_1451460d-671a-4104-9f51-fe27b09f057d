package package_glider.ms_glider.glider.crud;

public class GliderUpdate {
    private String vpnIp;
    private String vpnNetworkId;
    private String pixhawkUuid;
    private Integer autopilotSoftwareVersionId;
    private Integer desiredAutopilotSoftwareVersionId;
    private Integer jetsonSoftwareVersionId;
    private Integer desiredJetsonSoftwareVersionId;
    private Integer ftsPixhawkSoftwareVersionId;
    private Integer desiredFtsPixhawkSoftwareVersionId;
    private Integer ftsRaspiSoftwareVersionId;
    private Integer desiredFtsRaspiSoftwareVersionId;
    private Boolean inUse;
    private Boolean registrationComplete;
    private Integer regionId;

    public String getVpnIp() {
        return vpnIp;
    }
    public void setVpnIp(String vpnIp) {
        this.vpnIp = vpnIp;
    }
    public String getVpnNetworkId() {
        return vpnNetworkId;
    }
    public void setVpnNetworkId(String vpnNetworkId) {
        this.vpnNetworkId = vpnNetworkId;
    }
    public String getPixhawkUuid() {
        return pixhawkUuid;
    }
    public void setPixhawkUuid(String pixhawkUuid) {
        this.pixhawkUuid = pixhawkUuid;
    }
    public Boolean getInUse() {
        return inUse;
    }
    public void setInUse(Boolean inUse) {
        this.inUse = inUse;
    }
    public Boolean getRegistrationComplete() {
        return registrationComplete;
    }
    public void setRegistrationComplete(Boolean registrationComplete) {
        this.registrationComplete = registrationComplete;
    }
    public Integer getRegionId() {
        return regionId;
    }
    public void setRegionId(Integer regionId) {
        this.regionId = regionId;
    }
    public Integer getAutopilotSoftwareVersionId() {
        return autopilotSoftwareVersionId;
    }
    public void setAutopilotSoftwareVersionId(Integer autopilotSoftwareVersionId) {
        this.autopilotSoftwareVersionId = autopilotSoftwareVersionId;
    }
    public Integer getDesiredAutopilotSoftwareVersionId() {
        return desiredAutopilotSoftwareVersionId;
    }
    public void setDesiredAutopilotSoftwareVersionId(Integer desiredAutopilotSoftwareVersionId) {
        this.desiredAutopilotSoftwareVersionId = desiredAutopilotSoftwareVersionId;
    }
    public Integer getJetsonSoftwareVersionId() {
        return jetsonSoftwareVersionId;
    }
    public void setJetsonSoftwareVersionId(Integer jetsonSoftwareVersionId) {
        this.jetsonSoftwareVersionId = jetsonSoftwareVersionId;
    }
    public Integer getDesiredJetsonSoftwareVersionId() {
        return desiredJetsonSoftwareVersionId;
    }
    public void setDesiredJetsonSoftwareVersionId(Integer desiredJetsonSoftwareVersionId) {
        this.desiredJetsonSoftwareVersionId = desiredJetsonSoftwareVersionId;
    }
    public Integer getFtsPixhawkSoftwareVersionId() {
        return ftsPixhawkSoftwareVersionId;
    }
    public void setFtsPixhawkSoftwareVersionId(Integer ftsPixhawkSoftwareVersionId) {
        this.ftsPixhawkSoftwareVersionId = ftsPixhawkSoftwareVersionId;
    }
    public Integer getDesiredFtsPixhawkSoftwareVersionId() {
        return desiredFtsPixhawkSoftwareVersionId;
    }
    public void setDesiredFtsPixhawkSoftwareVersionId(Integer desiredFtsPixhawkSoftwareVersionId) {
        this.desiredFtsPixhawkSoftwareVersionId = desiredFtsPixhawkSoftwareVersionId;
    }
    public Integer getFtsRaspiSoftwareVersionId() {
        return ftsRaspiSoftwareVersionId;
    }
    public void setFtsRaspiSoftwareVersionId(Integer ftsRaspiSoftwareVersionId) {
        this.ftsRaspiSoftwareVersionId = ftsRaspiSoftwareVersionId;
    }
    public Integer getDesiredFtsRaspiSoftwareVersionId() {
        return desiredFtsRaspiSoftwareVersionId;
    }
    public void setDesiredFtsRaspiSoftwareVersionId(Integer desiredFtsRaspiSoftwareVersionId) {
        this.desiredFtsRaspiSoftwareVersionId = desiredFtsRaspiSoftwareVersionId;
    }

}