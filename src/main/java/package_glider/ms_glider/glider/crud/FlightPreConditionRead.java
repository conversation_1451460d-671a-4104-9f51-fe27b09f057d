package package_glider.ms_glider.glider.crud;



public class FlightPreConditionRead {
    private Boolean isMaintenanceRequiredFlag;
    private String gliderInformationLink;
    private Boolean isAllowedToFly;

    public static FlightPreConditionRead from(final Boolean isMaintenanceRequiredFlag, final String gliderInformationLink, final Boolean isAllowedToFly) {
        final FlightPreConditionRead flightPreConditionRead = new FlightPreConditionRead();
        flightPreConditionRead.setIsMaintenanceRequiredFlag(isMaintenanceRequiredFlag);
        flightPreConditionRead.setGliderInformationLink(gliderInformationLink);
        flightPreConditionRead.setIsAllowedToFly(isAllowedToFly);

        return flightPreConditionRead;
    }

    public Boolean getIsMaintenanceRequiredFlag() {
        return isMaintenanceRequiredFlag;
    }

    public void setIsMaintenanceRequiredFlag(Boolean isMaintenanceRequiredFlag) {
        this.isMaintenanceRequiredFlag = isMaintenanceRequiredFlag;
    }

    public String getGliderInformationLink() {
        return gliderInformationLink;
    }

    public void setGliderInformationLink(String GliderInformationLink) {
        this.gliderInformationLink = GliderInformationLink;
    }

    public Boolean getIsAllowedToFly() {
        return isAllowedToFly;
    }

    public void setIsAllowedToFly(Boolean isAllowedToFly) {
        this.isAllowedToFly = isAllowedToFly;
    }

}
