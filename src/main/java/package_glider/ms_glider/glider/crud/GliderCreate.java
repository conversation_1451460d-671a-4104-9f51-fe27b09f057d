package package_glider.ms_glider.glider.crud;

import java.time.LocalDate;
import java.time.LocalDateTime;
import package_glider.ms_glider.glider.model.Company;
import package_glider.ms_glider.glider.model.Glider;
import package_glider.ms_glider.glider.model.GliderMode;
import package_glider.ms_glider.live_glider_data.model.GliderStatus;
import package_glider.ms_glider.region.model.Region;
import package_glider.ms_glider.software_version.model.SoftwareVersion;




public class GliderCreate {
    private String line;
    private String generation;
    private String number;
    private String name;
    private String pixhawkUuid;
    private Integer gliderModeId;
    private Integer gliderStatusId;
    private Integer autopilotSoftwareVersionId; 
    private Integer desiredAutopilotSoftwareVersionId;
    private Integer jetsonSoftwareVersionId;
    private Integer desiredJetsonSoftwareVersionId;
    private Integer ftsPixhawkSoftwareVersionId;
    private Integer desiredFtsPixhawkSoftwareVersionId;
    private Integer ftsRaspiSoftwareVersionId;
    private Integer desiredFtsRaspiSoftwareVersionId;
    private Integer companyId;
    private Integer regionId;
    private String vpnIp;
    private String vpnNetworkId;
    private LocalDate manufacturingDate;
    private String registrationCode;
    private Boolean registrationComplete;
    private Boolean inUse;
    private String designDeviation;
    private String designComplianceRecord;
    private Long totalFlightTimeInSeconds;
    private Long totalFlightTimeSinceLastMaintenanceInSeconds;
    private LocalDateTime createdAt;
    private LocalDateTime updatedAt;


    public static Glider to(
        final GliderCreate item, 
        final GliderMode gliderMode, 
        final GliderStatus gliderStatus, 
        final Company company, 
        final SoftwareVersion autopilotSoftwareVersion,
        final SoftwareVersion desiredAutopilotSoftwareVersion,
        final SoftwareVersion jetsonSoftwareVersion,
        final SoftwareVersion desiredJetsonSoftwareVersion,
        final SoftwareVersion ftsPixhawkSoftwareVersion,
        final SoftwareVersion desiredFtsPixhawkSoftwareVersion,
        final SoftwareVersion ftsRaspiSoftwareVersion,
        final SoftwareVersion desiredFtsRaspiSoftwareVersion,
        final Region region) {
        final Glider res = new Glider();
        res.setLine(item.getLine());
        res.setGeneration(item.getGeneration());
        res.setNumber(item.getNumber());
        res.setName(item.getName());
        res.setPixhawkUuid(item.getPixhawkUuid());
        res.setGliderMode(gliderMode);
        res.setGliderStatus(gliderStatus);
        res.setAutopilotSoftwareVersion(autopilotSoftwareVersion);
        res.setDesiredAutopilotSoftwareVersion(desiredAutopilotSoftwareVersion);
        res.setJetsonSoftwareVersion(jetsonSoftwareVersion);
        res.setDesiredJetsonSoftwareVersion(desiredJetsonSoftwareVersion);
        res.setFtsPixhawkSoftwareVersion(ftsPixhawkSoftwareVersion);
        res.setDesiredFtsPixhawkSoftwareVersion(desiredFtsPixhawkSoftwareVersion);
        res.setFtsRaspiSoftwareVersion(ftsRaspiSoftwareVersion);
        res.setDesiredFtsRaspiSoftwareVersion(desiredFtsRaspiSoftwareVersion);
        res.setCompany(company);
        res.setRegion(region);
        res.setVpnIp(item.getVpnIp());
        res.setVpnNetworkId(item.getVpnNetworkId());
        res.setManufacturingDate(item.getManufacturingDate());
        res.setRegistrationCode(item.getRegistrationCode());
        res.setRegistrationComplete(item.getRegistrationComplete());
        res.setInUse(item.getInUse());
        res.setDesignDeviation(item.getDesignDeviation());
        res.setDesignComplianceRecord(item.getDesignComplianceRecord());
        if(item.getTotalFlightTimeInSeconds() != null) {
            res.setTotalFlightTimeInSeconds(item.getTotalFlightTimeInSeconds());
        }
        else {
            res.setTotalFlightTimeInSeconds(0L);
        }
        if(item.getTotalFlightTimeSinceLastMaintenanceInSeconds() != null) {
            res.setTotalFlightTimeSinceLastMaintenanceInSeconds(item.getTotalFlightTimeSinceLastMaintenanceInSeconds());
        }
        else {
            res.setTotalFlightTimeSinceLastMaintenanceInSeconds(0L);
        }
        res.setCreatedAt(item.getCreatedAt());
        res.setUpdatedAt(item.getUpdatedAt());

        return res;
    }


    public String getLine() {
        return line;
    }


    public void setLine(String line) {
        this.line = line;
    }


    public String getGeneration() {
        return generation;
    }


    public void setGeneration(String generation) {
        this.generation = generation;
    }


    public String getNumber() {
        return number;
    }


    public void setNumber(String number) {
        this.number = number;
    }

    


    public Integer getGliderModeId() {
        return gliderModeId;
    }


    public void setGliderModeId(Integer gliderModeId) {
        this.gliderModeId = gliderModeId;
    }


    public LocalDateTime getCreatedAt() {
        return createdAt;
    }


    public void setCreatedAt(LocalDateTime createdAt) {
        this.createdAt = createdAt;
    }


    public LocalDateTime getUpdatedAt() {
        return updatedAt;
    }


    public void setUpdatedAt(LocalDateTime updatedAt) {
        this.updatedAt = updatedAt;
    }


    public String getVpnIp() {
        return vpnIp;
    }


    public void setVpnIp(String vpnIp) {
        this.vpnIp = vpnIp;
    }


    public String getVpnNetworkId() {
        return vpnNetworkId;
    }


    public void setVpnNetworkId(String vpnNetworkId) {
        this.vpnNetworkId = vpnNetworkId;
    }


    public String getName() {
        return name;
    }


    public void setName(String name) {
        this.name = name;
    }


    public String getPixhawkUuid() {
        return pixhawkUuid;
    }


    public void setPixhawkUuid(String pixhawkUuid) {
        this.pixhawkUuid = pixhawkUuid;
    }


    public Integer getCompanyId() {
        return companyId;
    }


    public void setCompanyId(Integer companyId) {
        this.companyId = companyId;
    }


    public Integer getRegionId() {
        return regionId;
    }


    public void setRegionId(Integer regionId) {
        this.regionId = regionId;
    }




    public String getRegistrationCode() {
        return registrationCode;
    }


    public void setRegistrationCode(String registrationCode) {
        this.registrationCode = registrationCode;
    }


    public Boolean getRegistrationComplete() {
        return registrationComplete;
    }


    public void setRegistrationComplete(Boolean registrationComplete) {
        this.registrationComplete = registrationComplete;
    }


    public Integer getGliderStatusId() {
        return gliderStatusId;
    }


    public void setGliderStatusId(Integer gliderStatusId) {
        this.gliderStatusId = gliderStatusId;
    }


    public LocalDate getManufacturingDate() {
        return manufacturingDate;
    }


    public void setManufacturingDate(LocalDate manufacturingDate) {
        this.manufacturingDate = manufacturingDate;
    }


    public Boolean getInUse() {
        return inUse;
    }


    public void setInUse(Boolean inUse) {
        this.inUse = inUse;
    }


    public String getDesignDeviation() {
        return designDeviation;
    }


    public void setDesignDeviation(String designDeviation) {
        this.designDeviation = designDeviation;
    }


    public String getDesignComplianceRecord() {
        return designComplianceRecord;
    }


    public void setDesignComplianceRecord(String designComplianceRecord) {
        this.designComplianceRecord = designComplianceRecord;
    }


    public Long getTotalFlightTimeInSeconds() {
        return totalFlightTimeInSeconds;
    }


    public void setTotalFlightTimeInSeconds(Long totalFlightTimeInSeconds) {
        this.totalFlightTimeInSeconds = totalFlightTimeInSeconds;
    }


    public Long getTotalFlightTimeSinceLastMaintenanceInSeconds() {
        return totalFlightTimeSinceLastMaintenanceInSeconds;
    }


    public void setTotalFlightTimeSinceLastMaintenanceInSeconds(Long totalFlightTimeSinceLastMaintenanceInSeconds) {
        this.totalFlightTimeSinceLastMaintenanceInSeconds = totalFlightTimeSinceLastMaintenanceInSeconds;
    }


    public Integer getAutopilotSoftwareVersionId() {
        return autopilotSoftwareVersionId;
    }


    public void setAutopilotSoftwareVersionId(Integer autopilotSoftwareVersionId) {
        this.autopilotSoftwareVersionId = autopilotSoftwareVersionId;
    }


    public Integer getDesiredAutopilotSoftwareVersionId() {
        return desiredAutopilotSoftwareVersionId;
    }


    public void setDesiredAutopilotSoftwareVersionId(Integer desiredAutopilotSoftwareVersionId) {
        this.desiredAutopilotSoftwareVersionId = desiredAutopilotSoftwareVersionId;
    }


    public Integer getJetsonSoftwareVersionId() {
        return jetsonSoftwareVersionId;
    }


    public void setJetsonSoftwareVersionId(Integer jetsonSoftwareVersionId) {
        this.jetsonSoftwareVersionId = jetsonSoftwareVersionId;
    }


    public Integer getDesiredJetsonSoftwareVersionId() {
        return desiredJetsonSoftwareVersionId;
    }


    public void setDesiredJetsonSoftwareVersionId(Integer desiredJetsonSoftwareVersionId) {
        this.desiredJetsonSoftwareVersionId = desiredJetsonSoftwareVersionId;
    }


    public Integer getFtsPixhawkSoftwareVersionId() {
        return ftsPixhawkSoftwareVersionId;
    }


    public void setFtsPixhawkSoftwareVersionId(Integer ftsPixhawkSoftwareVersionId) {
        this.ftsPixhawkSoftwareVersionId = ftsPixhawkSoftwareVersionId;
    }


    public Integer getDesiredFtsPixhawkSoftwareVersionId() {
        return desiredFtsPixhawkSoftwareVersionId;
    }


    public void setDesiredFtsPixhawkSoftwareVersionId(Integer desiredFtsPixhawkSoftwareVersionId) {
        this.desiredFtsPixhawkSoftwareVersionId = desiredFtsPixhawkSoftwareVersionId;
    }


    public Integer getFtsRaspiSoftwareVersionId() {
        return ftsRaspiSoftwareVersionId;
    }


    public void setFtsRaspiSoftwareVersionId(Integer ftsRaspiSoftwareVersionId) {
        this.ftsRaspiSoftwareVersionId = ftsRaspiSoftwareVersionId;
    }


    public Integer getDesiredFtsRaspiSoftwareVersionId() {
        return desiredFtsRaspiSoftwareVersionId;
    }


    public void setDesiredFtsRaspiSoftwareVersionId(Integer desiredFtsRaspiSoftwareVersionId) {
        this.desiredFtsRaspiSoftwareVersionId = desiredFtsRaspiSoftwareVersionId;
    }

    

}