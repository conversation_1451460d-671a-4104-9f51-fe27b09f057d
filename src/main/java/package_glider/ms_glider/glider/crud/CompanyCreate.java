package package_glider.ms_glider.glider.crud;
import java.time.LocalDateTime;
import package_glider.ms_glider.glider.model.Company;

public class CompanyCreate {
    private String name;
    private LocalDateTime createdAt;
    private LocalDateTime updatedAt;

    public static Company to(final CompanyCreate item) {
        final Company res = new Company();
        res.setName(item.getName());
        res.setCreatedAt(item.getCreatedAt());
        res.setUpdatedAt(item.getUpdatedAt());

        return res;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public LocalDateTime getCreatedAt() {
        return createdAt;
    }

    public void setCreatedAt(LocalDateTime createdAt) {
        this.createdAt = createdAt;
    }

    public LocalDateTime getUpdatedAt() {
        return updatedAt;
    }

    public void setUpdatedAt(LocalDateTime updatedAt) {
        this.updatedAt = updatedAt;
    }
}
