package package_glider.ms_glider.glider.crud;
import java.time.LocalDate;
import java.time.LocalDateTime;
import package_glider.ms_glider.glider.model.Company;
import package_glider.ms_glider.glider.model.Glider;
import package_glider.ms_glider.glider.model.GliderMode;
import package_glider.ms_glider.live_glider_data.model.GliderStatus;
import package_glider.ms_glider.region.model.Region;
import package_glider.ms_glider.software_version.model.SoftwareVersion;
import package_glider.ms_glider.glider.util.FlightPreConditionUtil;



public class GliderRead {
    private Integer id;
    private String line;
    private String generation;
    private String number;
    private String name;
    private String pixhawkUuid;
    private GliderMode gliderMode;
    private GliderStatus gliderStatus;
    private SoftwareVersion autopilotSoftwareVersion;
    private SoftwareVersion desiredAutopilotSoftwareVersion;
    private SoftwareVersion jetsonSoftwareVersion;
    private SoftwareVersion desiredJetsonSoftwareVersion;
    private SoftwareVersion ftsPixhawkSoftwareVersion;
    private SoftwareVersion desiredFtsPixhawkSoftwareVersion;
    private SoftwareVersion ftsRaspiSoftwareVersion;
    private SoftwareVersion desiredFtsRaspiSoftwareVersion;
    private Company company;
    private Region region;
    private String vpnIp;
    private String vpnNetworkId;
    private LocalDate manufacturingDate;
    private String registrationCode;
    private Boolean registrationComplete;
    private Boolean inUse;
    private String designDeviation;
    private String designComplianceRecord;
    private Long totalFlightTimeInSeconds;
    private Long totalFlightTimeSinceLastMaintenanceInSeconds;
    private LocalDateTime createdAt;
    private LocalDateTime updatedAt;
    private FlightPreConditionRead flightPreConditionRead;


    public static GliderRead from(final Glider item) {
        final FlightPreConditionUtil flightPreConditionUtil = new FlightPreConditionUtil();
        final GliderRead glider = new GliderRead();
        glider.setId(item.getId());
        glider.setLine(item.getLine());
        glider.setGeneration(item.getGeneration());
        glider.setNumber(item.getNumber());
        glider.setName(item.getName());
        glider.setPixhawkUuid(item.getPixhawkUuid());
        glider.setGliderMode(item.getGliderMode());
        glider.setGliderStatus(item.getGliderStatus());
        glider.setAutopilotSoftwareVersion(item.getAutopilotSoftwareVersion());
        glider.setDesiredAutopilotSoftwareVersion(item.getDesiredAutopilotSoftwareVersion());
        glider.setJetsonSoftwareVersion(item.getJetsonSoftwareVersion());
        glider.setDesiredJetsonSoftwareVersion(item.getDesiredJetsonSoftwareVersion());
        glider.setFtsPixhawkSoftwareVersion(item.getFtsPixhawkSoftwareVersion());
        glider.setDesiredFtsPixhawkSoftwareVersion(item.getDesiredFtsPixhawkSoftwareVersion());
        glider.setFtsRaspiSoftwareVersion(item.getFtsRaspiSoftwareVersion());
        glider.setDesiredFtsRaspiSoftwareVersion(item.getDesiredFtsRaspiSoftwareVersion());
        glider.setCompany(item.getCompany());
        glider.setRegion(item.getRegion());
        glider.setVpnIp(item.getVpnIp());
        glider.setVpnNetworkId(item.getVpnNetworkId());
        glider.setManufacturingDate(item.getManufacturingDate());
        glider.setRegistrationCode(item.getRegistrationCode());
        glider.setRegistrationComplete(item.getRegistrationComplete());
        glider.setInUse(item.getInUse());
        glider.setDesignDeviation(item.getDesignDeviation());
        glider.setDesignComplianceRecord(item.getDesignComplianceRecord());
        glider.setTotalFlightTimeInSeconds(item.getTotalFlightTimeInSeconds());
        glider.setTotalFlightTimeSinceLastMaintenanceInSeconds(item.getTotalFlightTimeSinceLastMaintenanceInSeconds());
        glider.setCreatedAt(item.getCreatedAt());
        glider.setUpdatedAt(item.getUpdatedAt());
        glider.setFlightPreConditionRead(flightPreConditionUtil.checkMaintenanceRequiredFlag(item), flightPreConditionUtil.getGliderInformationLink(item), flightPreConditionUtil.generateIsAllowedToFly(item));
        return glider;
    }

    public Integer getId() {
        return id;
    }

    public void setId(Integer id) {
        this.id = id;
    }

    public String getLine() {
        return line;
    }

    public void setLine(String line) {
        this.line = line;
    }

    public String getGeneration() {
        return generation;
    }

    public void setGeneration(String generation) {
        this.generation = generation;
    }

    public String getNumber() {
        return number;
    }

    public void setNumber(String number) {
        this.number = number;
    }

    public LocalDateTime getCreatedAt() {
        return createdAt;
    }

    public void setCreatedAt(LocalDateTime createdAt) {
        this.createdAt = createdAt;
    }

    public LocalDateTime getUpdatedAt() {
        return updatedAt;
    }

    public void setUpdatedAt(LocalDateTime updatedAt) {
        this.updatedAt = updatedAt;
    }

    public GliderMode getGliderMode() {
        return gliderMode;
    }

    public void setGliderMode(GliderMode gliderMode) {
        this.gliderMode = gliderMode;
    }

    

    public String getVpnIp() {
        return vpnIp;
    }

    public void setVpnIp(String vpnIp) {
        this.vpnIp = vpnIp;
    }

    public String getVpnNetworkId() {
        return vpnNetworkId;
    }

    public void setVpnNetworkId(String vpnNetworkId) {
        this.vpnNetworkId = vpnNetworkId;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public String getPixhawkUuid() {
        return pixhawkUuid;
    }

    public void setPixhawkUuid(String pixhawkUuid) {
        this.pixhawkUuid = pixhawkUuid;
    }

    public Company getCompany() {
        return company;
    }

    public void setCompany(Company company) {
        this.company = company;
    }


    public Region getRegion() {
        return region;
    }

    public void setRegion(Region region) {
        this.region = region;
    }


    public String getRegistrationCode() {
        return registrationCode;
    }

    public void setRegistrationCode(String registrationCode) {
        this.registrationCode = registrationCode;
    }

    public Boolean getRegistrationComplete() {
        return registrationComplete;
    }

    public void setRegistrationComplete(Boolean registrationComplete) {
        this.registrationComplete = registrationComplete;
    }

    public GliderStatus getGliderStatus() {
        return gliderStatus;
    }

    public void setGliderStatus(GliderStatus gliderStatus) {
        this.gliderStatus = gliderStatus;
    }

    public LocalDate getManufacturingDate() {
        return manufacturingDate;
    }

    public void setManufacturingDate(LocalDate manufacturingDate) {
        this.manufacturingDate = manufacturingDate;
    }

    public Boolean getInUse() {
        return inUse;
    }

    public void setInUse(Boolean inUse) {
        this.inUse = inUse;
    }

    public String getDesignDeviation() {
        return designDeviation;
    }

    public void setDesignDeviation(String designDeviation) {
        this.designDeviation = designDeviation;
    }

    public String getDesignComplianceRecord() {
        return designComplianceRecord;
    }

    public void setDesignComplianceRecord(String designComplianceRecord) {
        this.designComplianceRecord = designComplianceRecord;
    }

    public Long getTotalFlightTimeInSeconds() {
        return totalFlightTimeInSeconds;
    }

    public void setTotalFlightTimeInSeconds(Long totalFlightTimeInSeconds) {
        this.totalFlightTimeInSeconds = totalFlightTimeInSeconds;
    }

    public Long getTotalFlightTimeSinceLastMaintenanceInSeconds() {
        return totalFlightTimeSinceLastMaintenanceInSeconds;
    }

    public void setTotalFlightTimeSinceLastMaintenanceInSeconds(Long totalFlightTimeSinceLastMaintenanceInSeconds) {
        this.totalFlightTimeSinceLastMaintenanceInSeconds = totalFlightTimeSinceLastMaintenanceInSeconds;
    }

    public FlightPreConditionRead getFlightPreConditionRead() {
        return flightPreConditionRead;
    }

    public void setFlightPreConditionRead(Boolean isMaintenanceRequiredFlag, String gliderInformationLink, Boolean isAllowedToFly) {
        this.flightPreConditionRead = FlightPreConditionRead.from(isMaintenanceRequiredFlag, gliderInformationLink, isAllowedToFly);
    }

    public SoftwareVersion getAutopilotSoftwareVersion() {
        return autopilotSoftwareVersion;
    }

    public void setAutopilotSoftwareVersion(SoftwareVersion autopilotSoftwareVersion) {
        this.autopilotSoftwareVersion = autopilotSoftwareVersion;
    }

    public SoftwareVersion getDesiredAutopilotSoftwareVersion() {
        return desiredAutopilotSoftwareVersion;
    }

    public void setDesiredAutopilotSoftwareVersion(SoftwareVersion desiredAutopilotSoftwareVersion) {
        this.desiredAutopilotSoftwareVersion = desiredAutopilotSoftwareVersion;
    }

    public SoftwareVersion getJetsonSoftwareVersion() {
        return jetsonSoftwareVersion;
    }

    public void setJetsonSoftwareVersion(SoftwareVersion jetsonSoftwareVersion) {
        this.jetsonSoftwareVersion = jetsonSoftwareVersion;
    }

    public SoftwareVersion getDesiredJetsonSoftwareVersion() {
        return desiredJetsonSoftwareVersion;
    }

    public void setDesiredJetsonSoftwareVersion(SoftwareVersion desiredJetsonSoftwareVersion) {
        this.desiredJetsonSoftwareVersion = desiredJetsonSoftwareVersion;
    }

    public SoftwareVersion getFtsPixhawkSoftwareVersion() {
        return ftsPixhawkSoftwareVersion;
    }

    public void setFtsPixhawkSoftwareVersion(SoftwareVersion ftsPixhawkSoftwareVersion) {
        this.ftsPixhawkSoftwareVersion = ftsPixhawkSoftwareVersion;
    }

    public SoftwareVersion getDesiredFtsPixhawkSoftwareVersion() {
        return desiredFtsPixhawkSoftwareVersion;
    }

    public void setDesiredFtsPixhawkSoftwareVersion(SoftwareVersion desiredFtsPixhawkSoftwareVersion) {
        this.desiredFtsPixhawkSoftwareVersion = desiredFtsPixhawkSoftwareVersion;
    }

    public SoftwareVersion getFtsRaspiSoftwareVersion() {
        return ftsRaspiSoftwareVersion;
    }

    public void setFtsRaspiSoftwareVersion(SoftwareVersion ftsRaspiSoftwareVersion) {
        this.ftsRaspiSoftwareVersion = ftsRaspiSoftwareVersion;
    }

    public SoftwareVersion getDesiredFtsRaspiSoftwareVersion() {
        return desiredFtsRaspiSoftwareVersion;
    }

    public void setDesiredFtsRaspiSoftwareVersion(SoftwareVersion desiredFtsRaspiSoftwareVersion) {
        this.desiredFtsRaspiSoftwareVersion = desiredFtsRaspiSoftwareVersion;
    }

    public void setFlightPreConditionRead(FlightPreConditionRead flightPreConditionRead) {
        this.flightPreConditionRead = flightPreConditionRead;
    }
    
}