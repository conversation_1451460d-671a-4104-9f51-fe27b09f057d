package package_glider.ms_glider.glider.crud;
import java.time.LocalDateTime;
import package_glider.ms_glider.glider.model.Company;

public class CompanyRead {
    private Integer id;
    private String name;
    private LocalDateTime createdAt;
    private LocalDateTime updatedAt;

    public static CompanyRead from(final Company item) {
        final CompanyRead company = new CompanyRead();
        company.setId(item.getId());
        company.setName(item.getName());
        company.setCreatedAt(item.getCreatedAt());
        company.setUpdatedAt(item.getUpdatedAt());
        return company;
    }

    public Integer getId() {
        return id;
    }

    public void setId(Integer id) {
        this.id = id;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public LocalDateTime getCreatedAt() {
        return createdAt;
    }

    public void setCreatedAt(LocalDateTime createdAt) {
        this.createdAt = createdAt;
    }

    public LocalDateTime getUpdatedAt() {
        return updatedAt;
    }

    public void setUpdatedAt(LocalDateTime updatedAt) {
        this.updatedAt = updatedAt;
    }

    
}
