package package_glider.ms_glider.glider.crud;
import java.time.LocalDateTime;
import package_glider.ms_glider.glider.model.GliderMode;

public class GliderModeRead {
    private Integer id;
    private String name;
    private String description;
    private LocalDateTime createdAt;
    private LocalDateTime updatedAt;

    public static GliderModeRead from(final GliderMode item) {
        final GliderModeRead gliderMode = new GliderModeRead();
        gliderMode.setId(item.getId());
        gliderMode.setName(item.getName());
        gliderMode.setDescription(item.getDescription());
        gliderMode.setCreatedAt(item.getCreatedAt());
        gliderMode.setUpdatedAt(item.getUpdatedAt());
        return gliderMode;
    }

    public Integer getId() {
        return id;
    }

    public void setId(Integer id) {
        this.id = id;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public String getDescription() {
        return description;
    }

    public void setDescription(String description) {
        this.description = description;
    }

    public LocalDateTime getCreatedAt() {
        return createdAt;
    }

    public void setCreatedAt(LocalDateTime createdAt) {
        this.createdAt = createdAt;
    }

    public LocalDateTime getUpdatedAt() {
        return updatedAt;
    }

    public void setUpdatedAt(LocalDateTime updatedAt) {
        this.updatedAt = updatedAt;
    }

    
}
