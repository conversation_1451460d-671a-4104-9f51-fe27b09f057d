package package_glider.ms_glider.glider.service;

import java.util.Optional;
import java.util.stream.Stream;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Component;
import package_glider.ms_glider.glider.crud.GliderUpdate;
import package_glider.ms_glider.glider.model.Glider;
import package_glider.ms_glider.glider.repository.GliderRepository;
import package_glider.ms_glider.region.model.Region;
import package_glider.ms_glider.software_version.model.SoftwareVersion;
import package_glider.ms_glider.live_glider_data.model.GliderStatus;
import package_glider.ms_glider.live_glider_data.service.GliderStatusService;
import package_glider.ms_glider.logs.service.GliderStatusLogService;
import package_glider.ms_glider.logs.crud.GliderStatusLogCreate;




@Component
@RequiredArgsConstructor
public final class GliderService {
    private final GliderStatusService gliderStatusService;
    private final GliderStatusLogService gliderStatusLogService;
    private final GliderRepository gliderRepository;

    public Optional<Glider> findById(final Integer id) {
        return gliderRepository.findById(id);
    }

    public Optional<Glider> findByName(final String name) {
        return gliderRepository.findByName(name);
    }

    public Optional<Glider> findByVpnIp(final String vpnIp) {
        return gliderRepository.findByVpnIp(vpnIp);
    }

    public Optional<Glider> findByPixhawkUuid(final String pixhawkUuid) {
        return gliderRepository.findByPixhawkUuid(pixhawkUuid);
    }

    public Optional<Glider> findByRegistrationCode(final String registrationCode) {
        return gliderRepository.findByRegistrationCode(registrationCode);
    }

    public Stream<Glider> list() {
        return gliderRepository.findAll().stream();
    }

    public Stream<Glider> listInUse(final Boolean inUse) {
        return list().filter(glider -> glider.getInUse() == inUse);
    }

    public Optional<Glider> add(final Glider glider) {
        final Glider savedGlider = gliderRepository.save(glider);
        return Optional.of(savedGlider);
    }

    public Optional<Glider> resetTotalFlightTimeSinceLastMaintenanceInSeconds(final Integer id) {
        return gliderRepository.findById(id).map(glider -> {
            glider.setTotalFlightTimeSinceLastMaintenanceInSeconds(0L);
            return gliderRepository.save(glider);
        });
    }

    public Optional<Glider> addFlightTime(final String gliderName, final Long flightTimeInSeconds) {
        return gliderRepository.findByName(gliderName).map(glider -> {
            glider.setTotalFlightTimeInSeconds(glider.getTotalFlightTimeInSeconds() + flightTimeInSeconds);
            glider.setTotalFlightTimeSinceLastMaintenanceInSeconds(glider.getTotalFlightTimeSinceLastMaintenanceInSeconds() + flightTimeInSeconds);
            return gliderRepository.save(glider);
        });
    }

    public Optional<Glider> updatePixhawkUuid(final String gliderName, final String pixhawkUuid) {
        return gliderRepository.findByName(gliderName).map(glider -> {
            glider.setPixhawkUuid(pixhawkUuid);
            return gliderRepository.save(glider);
        });
    }

    public Optional<Glider> updateGlider(
        Integer id, 
        GliderUpdate request, 
        SoftwareVersion autopilSoftwareVersion,
        SoftwareVersion desiredAutopilotSoftwareVersion,
        SoftwareVersion jetsonSoftwareVersion,
        SoftwareVersion desiredJetsonSoftwareVersion,
        SoftwareVersion ftsPixhawkSoftwareVersion,
        SoftwareVersion desiredFtsPixhawkSoftwareVersion,
        SoftwareVersion ftsRaspiSoftwareVersion,
        SoftwareVersion desiredFtsRaspiSoftwareVersion,
        Region region) {
        return gliderRepository.findById(id).map(glider -> {
            if (request.getVpnIp() != null) {
                glider.setVpnIp(request.getVpnIp());
            }
            if (request.getVpnNetworkId() != null) {
                glider.setVpnNetworkId(request.getVpnNetworkId());
            }
            if (request.getPixhawkUuid() != null) {
                glider.setPixhawkUuid(request.getPixhawkUuid());
            }
            if (autopilSoftwareVersion != null) {
                glider.setAutopilotSoftwareVersion(autopilSoftwareVersion);
            }
            if (desiredAutopilotSoftwareVersion != null) {
                glider.setDesiredAutopilotSoftwareVersion(desiredAutopilotSoftwareVersion);
            }
            if (jetsonSoftwareVersion != null) {
                glider.setJetsonSoftwareVersion(jetsonSoftwareVersion);
            }
            if (desiredJetsonSoftwareVersion != null) {
                glider.setDesiredJetsonSoftwareVersion(desiredJetsonSoftwareVersion);
            }
            if (ftsPixhawkSoftwareVersion != null) {
                glider.setFtsPixhawkSoftwareVersion(ftsPixhawkSoftwareVersion);
            }
            if (desiredFtsPixhawkSoftwareVersion != null) {
                glider.setDesiredFtsPixhawkSoftwareVersion(desiredFtsPixhawkSoftwareVersion);
            }
            if (ftsRaspiSoftwareVersion != null) {
                glider.setFtsRaspiSoftwareVersion(ftsRaspiSoftwareVersion);
            }
            if (desiredFtsRaspiSoftwareVersion != null) {
                glider.setDesiredFtsRaspiSoftwareVersion(desiredFtsRaspiSoftwareVersion);
            }
            if (request.getInUse() != null) {
                glider.setInUse(request.getInUse());
            }
            if (request.getRegistrationComplete() != null) {
                glider.setRegistrationComplete(request.getRegistrationComplete());
            }
            if (region != null) {
                glider.setRegion(region);
            }
            
            return gliderRepository.save(glider);
        });
    }


    // Update glider statuses
    public Optional<Glider> updateGliderStatus(final String gliderStatusName, final String gliderName, final String note, final String userEmail) {
        // Find the glider status
        Optional<GliderStatus> gliderStatus = gliderStatusService.findByName(gliderStatusName);
        if (!gliderStatus.isPresent()) {
            return Optional.empty();
        }

        // Get current (old) glider status
        Optional<Glider> thisGlider = gliderRepository.findByName(gliderName);
        if (!thisGlider.isPresent()) {
            return Optional.empty();
        }

        GliderStatus oldGliderStatus = thisGlider.get().getGliderStatus();

        // Update the glider statuses logs
        GliderStatusLogCreate newGliderStatusLog = new GliderStatusLogCreate();
        newGliderStatusLog.setGliderName(gliderName);
        newGliderStatusLog.setNote(note);
        newGliderStatusLog.setUserEmail(userEmail);
        newGliderStatusLog.setNewGliderStatusId(gliderStatus.get().getId());
        newGliderStatusLog.setOldGliderStatusId(oldGliderStatus.getId());
        gliderStatusLogService.add(GliderStatusLogCreate.to(newGliderStatusLog, gliderStatus.get(), oldGliderStatus));
        

        return gliderRepository.findByName(gliderName).map(glider -> {
            glider.setGliderStatus(gliderStatus.get());
            return gliderRepository.save(glider);
        });
    }

    // TODO: Ensure that the transitions are restricted following the state machine
}