package package_glider.ms_glider.glider.service;

import java.util.Optional;
import java.util.stream.Stream;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Component;
import package_glider.ms_glider.glider.model.GliderMode;
import package_glider.ms_glider.glider.repository.GliderModeRepository;




@Component
@RequiredArgsConstructor
public final class GliderModeService {
    private final GliderModeRepository gliderModeRepository;

    public Optional<GliderMode> findById(final Integer id) {
        return id == null ? Optional.empty() : gliderModeRepository.findById(id);
    }

    public Stream<GliderMode> list() {
        return gliderModeRepository.findAll().stream();
    }

    public Optional<GliderMode> add(final GliderMode gliderMode) {
        return Optional.of(gliderModeRepository.save(gliderMode));
    }
}