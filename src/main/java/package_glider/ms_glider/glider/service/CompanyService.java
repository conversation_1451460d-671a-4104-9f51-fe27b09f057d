package package_glider.ms_glider.glider.service;

import java.util.Optional;
import java.util.stream.Stream;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Component;
import package_glider.ms_glider.glider.model.Company;
import package_glider.ms_glider.glider.repository.CompanyRepository;




@Component
@RequiredArgsConstructor
public final class CompanyService {
    private final CompanyRepository companyRepository;

    public Optional<Company> findById(final Integer id) {
        return id == null ? Optional.empty() : companyRepository.findById(id);
    }

    public Stream<Company> list() {
        return companyRepository.findAll().stream();
    }

    public Optional<Company> add(final Company company) {
        return Optional.of(companyRepository.save(company));
    }
}