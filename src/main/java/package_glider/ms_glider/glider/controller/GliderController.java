package package_glider.ms_glider.glider.controller;

import io.swagger.v3.oas.annotations.Operation;
import java.util.stream.Stream;
import lombok.RequiredArgsConstructor;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.CrossOrigin;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PatchMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;
import package_glider.ms_glider.glider.crud.GliderCreate;
import package_glider.ms_glider.glider.crud.GliderRead;
import package_glider.ms_glider.glider.crud.GliderUpdate;
import package_glider.ms_glider.glider.model.Company;
import package_glider.ms_glider.glider.model.GliderMode;
import package_glider.ms_glider.glider.service.CompanyService;
import package_glider.ms_glider.glider.service.GliderModeService;
import package_glider.ms_glider.glider.service.GliderService;
import package_glider.ms_glider.live_glider_data.model.GliderStatus;
import package_glider.ms_glider.live_glider_data.service.GliderStatusService;
import package_glider.ms_glider.region.model.Region;
import package_glider.ms_glider.region.service.RegionService;
import package_glider.ms_glider.software_version.model.SoftwareVersion;
import package_glider.ms_glider.software_version.service.SoftwareVersionService;





class NotFound extends RuntimeException {
}

@RestController
@RequestMapping("/api/gliders")
@CrossOrigin("*")
@RequiredArgsConstructor
public class GliderController {
    private final GliderService gliderService;
    private final GliderModeService gliderModeService;
    private final GliderStatusService gliderStatusService;
    private final CompanyService companyService;
    private final SoftwareVersionService softwareVersionService;
    private final RegionService regionService;

    
    @Operation(summary = "Get a glider by id")
    @GetMapping("/{id}")
    GliderRead result(final @PathVariable Integer id) {
        return gliderService.findById(id).map(item -> GliderRead.from(item)).orElseThrow(() -> new NotFound());
    }

    // TODO: change to query parameters
    @Operation(summary = "Get a glider by IP")
    @GetMapping("/vpn-ip/{vpnIp}")
    GliderRead result_by_vpn_ip(final @PathVariable String vpnIp) {
        return gliderService.findByVpnIp(vpnIp).map(item -> GliderRead.from(item)).orElseThrow(() -> new NotFound());
    }

    @Operation(summary = "Get a glider by pixhawk UUID")
    @GetMapping("/pixhawk-uuid/{pixhawkUuid}")
    GliderRead result_by_pixhawhk_uuid(final @PathVariable String pixhawkUuid) {
        return gliderService.findByPixhawkUuid(pixhawkUuid).map(item -> GliderRead.from(item))
                .orElseThrow(() -> new NotFound());
    }

    @Operation(summary = "Get a glider by name")
    @GetMapping("/name/{name}")
    GliderRead result_by_name(final @PathVariable String name) {
        return gliderService.findByName(name).map(item -> GliderRead.from(item)).orElseThrow(() -> new NotFound());
    }

    @Operation(summary = "Get a glider by registration code")
    @GetMapping("/registration-code/{registrationCode}")
    GliderRead result_by_registration_code(final @PathVariable String registrationCode) {
        return gliderService.findByRegistrationCode(registrationCode).map(item -> GliderRead.from(item))
                .orElseThrow(() -> new NotFound());
    }
    
    @Operation(summary = "List gliders")
    @GetMapping
    Stream<GliderRead> all(@RequestParam(required = false) Boolean inUse) {
        if (inUse != null) {
            return gliderService.listInUse(inUse).map(item -> GliderRead.from(item));
                
        } else {
            return gliderService.list().map(item -> GliderRead.from(item));
        }
    }

    @Operation(summary = "Register a new glider")
    @PostMapping
    GliderRead add(final @RequestBody @Validated GliderCreate glider) {
        // Check if the glider already exists
        if (gliderService.findByName(glider.getName()).isPresent()) {
            throw new NotFound();
        }
        return gliderService
                .add(GliderCreate.to(
                    glider, 
                    gliderMode(glider.getGliderModeId()), 
                    gliderStatus(glider.getGliderStatusId()),
                    company(glider.getCompanyId()), 
                    softwareVersion(glider.getAutopilotSoftwareVersionId()), 
                    softwareVersion(glider.getDesiredAutopilotSoftwareVersionId()),
                    softwareVersion(glider.getJetsonSoftwareVersionId()),
                    softwareVersion(glider.getDesiredJetsonSoftwareVersionId()),
                    softwareVersion(glider.getFtsPixhawkSoftwareVersionId()),
                    softwareVersion(glider.getDesiredFtsPixhawkSoftwareVersionId()),
                    softwareVersion(glider.getFtsRaspiSoftwareVersionId()),
                    softwareVersion(glider.getDesiredFtsRaspiSoftwareVersionId()),
                    region(glider.getRegionId())))
                .map(item -> GliderRead.from(item)).orElseThrow(() -> new NotFound());
    }

    @Operation(summary = "Accumulate the total flight time for a glider")
    @PatchMapping("/accumulate-flight-time")
    GliderRead accumulateFlightTime(final @RequestParam String gliderName, final @RequestParam Long flightTimeInSeconds) {
        return gliderService.addFlightTime(gliderName, flightTimeInSeconds).map(item -> GliderRead.from(item))
                .orElseThrow(() -> new NotFound());
    }
    
    @Operation(summary = "Update a glider by id")
    @PatchMapping("/{id}")
    GliderRead updateGlider(final @PathVariable Integer id, final @RequestBody @Validated GliderUpdate request) {
        return gliderService.updateGlider(
            id, 
            request, 
            softwareVersion(request.getAutopilotSoftwareVersionId()), 
            softwareVersion(request.getDesiredAutopilotSoftwareVersionId()),
            softwareVersion(request.getJetsonSoftwareVersionId()),
            softwareVersion(request.getDesiredJetsonSoftwareVersionId()),
            softwareVersion(request.getFtsPixhawkSoftwareVersionId()),
            softwareVersion(request.getDesiredFtsPixhawkSoftwareVersionId()),
            softwareVersion(request.getFtsRaspiSoftwareVersionId()),
            softwareVersion(request.getDesiredFtsRaspiSoftwareVersionId()),
            region(request.getRegionId()))
        .map(item -> GliderRead.from(item)).orElseThrow(() -> new NotFound());
    }


    @Operation(summary = "Update pixhawk UUID by glider name")
    @PatchMapping("/gliders/{gliderName}/update-pixhawk-uuid")
    GliderRead updatePixhawkUuid(final @RequestParam String pixhawkUuid, final @PathVariable String gliderName) {
        return gliderService.updatePixhawkUuid(gliderName, pixhawkUuid).map(item -> GliderRead.from(item))
                .orElseThrow(() -> new NotFound());
    }

    @Operation(summary = "Update glider status to Grounded")
    @PatchMapping("/{gliderName}/update-status/grounded")
    GliderRead updateGliderStatusToGrounded(final @RequestParam String note, final @RequestParam String userEmail, final @PathVariable String gliderName) {
        return gliderService.updateGliderStatus("Grounded", gliderName, note, userEmail).map(item -> GliderRead.from(item)).orElseThrow(() -> new NotFound());
    }

    @Operation(summary = "Update glider status to Ready")
    @PatchMapping("/{gliderName}/update-status/ready")
    GliderRead updateGliderStatusToReady(final @RequestParam String note, final @RequestParam String userEmail, final @PathVariable String gliderName) {
        return gliderService.updateGliderStatus("Ready", gliderName, note, userEmail).map(item -> GliderRead.from(item)).orElseThrow(() -> new NotFound());
    }

    @Operation(summary = "Update glider status to Unavailable")
    @PatchMapping("/{gliderName}/update-status/unavailable")
    GliderRead updateGliderStatusToUnavailable(final @RequestParam String note, final @RequestParam String userEmail, final @PathVariable String gliderName) {
        return gliderService.updateGliderStatus("Unavailable", gliderName, note, userEmail).map(item -> GliderRead.from(item)).orElseThrow(() -> new NotFound());
    }

    @Operation(summary = "Update glider status to Maintenance Due")
    @PatchMapping("/{gliderName}/update-status/maintenance-due")
    GliderRead updateGliderStatusToMaintenanceDue(final @RequestParam String note, final @RequestParam String userEmail, final @PathVariable String gliderName) {
        return gliderService.updateGliderStatus("Maintenance Due", gliderName, note, userEmail).map(item -> GliderRead.from(item)).orElseThrow(() -> new NotFound());
    }

    @Operation(summary = "Update glider status to Post Maintenance Checks")
    @PatchMapping("/{gliderName}/update-status/post-maintenance-checks")
    GliderRead updateGliderStatusToPostMaintenanceChecks(final @RequestParam String note, final @RequestParam String userEmail, final @PathVariable String gliderName) {
        return gliderService.updateGliderStatus("Post Maintenance Checks", gliderName, note, userEmail).map(item -> GliderRead.from(item)).orElseThrow(() -> new NotFound());
    }

    @Operation(summary = "Update glider status to Retired")
    @PatchMapping("/{gliderName}/update-status/retired")
    GliderRead updateGliderStatusToRetired(final @RequestParam String note, final @RequestParam String userEmail, final @PathVariable String gliderName) {
        return gliderService.updateGliderStatus("Retired", gliderName, note, userEmail).map(item -> GliderRead.from(item)).orElseThrow(() -> new NotFound());
    }


    private GliderMode gliderMode(final Integer gliderModeId) {
        return gliderModeService.findById(gliderModeId).orElse(null);
    }

    private GliderStatus gliderStatus(final Integer gliderStatusId) {
        return gliderStatusService.findById(gliderStatusId).orElse(gliderStatusService.findByName("Unavailable").orElse(null));
    }

    private Company company(final Integer companyId) {
        return companyService.findById(companyId).orElse(null);
    }

    private SoftwareVersion softwareVersion(final Integer softwareVersionId) {
        return softwareVersionService.findById(softwareVersionId).orElse(null);
    }

    private Region region(final Integer regionId) {
        return regionService.findById(regionId).orElse(null);
    }

}