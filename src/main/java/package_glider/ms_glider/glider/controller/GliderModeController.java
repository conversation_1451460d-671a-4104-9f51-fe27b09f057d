package package_glider.ms_glider.glider.controller;

import io.swagger.v3.oas.annotations.Operation;
import java.util.stream.Stream;
import lombok.RequiredArgsConstructor;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.CrossOrigin;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import package_glider.ms_glider.glider.crud.GliderModeCreate;
import package_glider.ms_glider.glider.crud.GliderModeRead;
import package_glider.ms_glider.glider.service.GliderModeService;







@RestController
@RequestMapping("/api/glider-modes")
@CrossOrigin("*")
@RequiredArgsConstructor
public class GliderModeController {
    private final GliderModeService gliderModeService;

    @Operation(summary = "List glider modes")
    @GetMapping
    Stream<GliderModeRead> all() {
        return gliderModeService.list().map(item -> GliderModeRead.from(item));
    }

    @Operation(summary = "Register a new glider mode")
    @PostMapping
    GliderModeRead add(final @RequestBody @Validated GliderModeCreate glider) {
        return gliderModeService.add(GliderModeCreate.to(glider))
                .map(item -> GliderModeRead.from(item)).orElseThrow(() -> new NotFound());
    }
}
