package package_glider.ms_glider.glider.controller;

import io.swagger.v3.oas.annotations.Operation;
import java.util.stream.Stream;
import lombok.RequiredArgsConstructor;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.CrossOrigin;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import package_glider.ms_glider.glider.crud.CompanyCreate;
import package_glider.ms_glider.glider.crud.CompanyRead;
import package_glider.ms_glider.glider.service.CompanyService;




@RestController
@RequestMapping("/api/companies")
@CrossOrigin("*")
@RequiredArgsConstructor
public class CompanyController {
    private final CompanyService companyService;

    @Operation(summary = "List all companies")
    @GetMapping
    Stream<CompanyRead> all() {
        return companyService.list().map(item -> CompanyRead.from(item));
    }

    @Operation(summary = "Register a new company")
    @PostMapping
    CompanyRead add(final @RequestBody @Validated CompanyCreate company) {
        return companyService.add(CompanyCreate.to(company))
                .map(item -> CompanyRead.from(item)).orElseThrow(() -> new NotFound());
    }
}
