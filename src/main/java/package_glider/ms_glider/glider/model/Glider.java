package package_glider.ms_glider.glider.model;

import jakarta.persistence.*;
import java.time.LocalDate;
import lombok.EqualsAndHashCode;
import package_glider.ms_glider.live_glider_data.model.GliderStatus;
import package_glider.ms_glider.region.model.Region;
import package_glider.ms_glider.software_version.model.SoftwareVersion;



// TODO: Check nullable fields


@Entity
@Table(name = "glider")
@EqualsAndHashCode(callSuper = true)
public final class Glider extends Base {
    
    @Column(name = "line")
    private String line;
    @Column(name = "generation")
    private String generation;
    @Column(name = "number")
    private String number;
    @Column(name = "name", unique = true, nullable = false)
    private String name;
    @Column(name = "pixhawk_uuid", unique = true, nullable = true)
    private String pixhawkUuid;
    @Column(name = "vpn_ip")
    private String vpnIp;
    @Column(name = "vpn_network_id")
    private String vpnNetworkId;
    @Column(name = "manufacturing_date")
    private LocalDate manufacturingDate;
    @Column(name = "registration_code")
    private String registrationCode;
    @Column(name = "registration_complete")
    private Boolean registrationComplete;
    @Column(name = "in_use")
    private Boolean inUse;
    @Column(name = "design_deviation")
    private String designDeviation;
    @Column(name = "design_compliance_record")
    private String designComplianceRecord;
    @Column(name = "total_flight_time_in_seconds")
    private Long totalFlightTimeInSeconds;
    @Column(name = "total_flight_time_since_last_maintenance_in_seconds")
    private Long totalFlightTimeSinceLastMaintenanceInSeconds;

    @ManyToOne
    @JoinColumn(name = "glider_mode_id", nullable = true)
    private GliderMode gliderMode;

    @ManyToOne
    @JoinColumn(name = "glider_status_id", nullable = false)
    private GliderStatus gliderStatus;

    @ManyToOne
    @JoinColumn(name = "company_id", nullable = true)
    private Company company;

    @ManyToOne
    @JoinColumn(name = "autopilot_software_version_id", nullable = true)
    private SoftwareVersion autopilotSoftwareVersion;

    @ManyToOne
    @JoinColumn(name = "desired_autopilot_software_version_id", nullable = true)
    private SoftwareVersion desiredAutopilotSoftwareVersion;
    
    @ManyToOne
    @JoinColumn(name = "jetson_software_version_id", nullable = true)
    private SoftwareVersion jetsonSoftwareVersion;

    @ManyToOne
    @JoinColumn(name = "desired_jetson_software_version_id", nullable = true)
    private SoftwareVersion desiredJetsonSoftwareVersion;

    @ManyToOne
    @JoinColumn(name = "fts_pixhawk_software_version_id", nullable = true)
    private SoftwareVersion ftsPixhawkSoftwareVersion;

    @ManyToOne
    @JoinColumn(name = "desired_fts_pixhawk_software_version_id", nullable = true)
    private SoftwareVersion desiredFtsPixhawkSoftwareVersion;

    @ManyToOne
    @JoinColumn(name = "fts_raspi_software_version_id", nullable = true)
    private SoftwareVersion ftsRaspiSoftwareVersion;

    @ManyToOne
    @JoinColumn(name = "desired_fts_raspi_software_version_id", nullable = true)
    private SoftwareVersion desiredFtsRaspiSoftwareVersion;

    @ManyToOne
    @JoinColumn(name = "region_id", nullable = true)
    private Region region;
    
    public String getLine() {
        return line;
    }

    public void setLine(String line) {
        this.line = line;
    }

    public String getGeneration() {
        return generation;
    }

    public void setGeneration(String generation) {
        this.generation = generation;
    }

    public String getNumber() {
        return number;
    }

    public void setNumber(String number) {
        this.number = number;
    }

    public GliderMode getGliderMode() {
        return gliderMode;
    }

    public void setGliderMode(GliderMode gliderMode) {
        this.gliderMode = gliderMode;
    }

    public String getVpnIp() {
        return vpnIp;
    }

    public void setVpnIp(String vpnIp) {
        this.vpnIp = vpnIp;
    }

    public String getVpnNetworkId() {
        return vpnNetworkId;
    }

    public void setVpnNetworkId(String vpnNetworkId) {
        this.vpnNetworkId = vpnNetworkId;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public String getPixhawkUuid() {
        return pixhawkUuid;
    }

    public void setPixhawkUuid(String pixhawkUuid) {
        this.pixhawkUuid = pixhawkUuid;
    }

    public Company getCompany() {
        return company;
    }

    public void setCompany(Company company) {
        this.company = company;
    }


    public String getRegistrationCode() {
        return registrationCode;
    }

    public void setRegistrationCode(String registrationCode) {
        this.registrationCode = registrationCode;
    }

    public Boolean getRegistrationComplete() {
        return registrationComplete;
    }

    public void setRegistrationComplete(Boolean registrationComplete) {
        this.registrationComplete = registrationComplete;
    }



    public Region getRegion() {
        return region;
    }

    public void setRegion(Region region) {
        this.region = region;
    }

    public GliderStatus getGliderStatus() {
        return gliderStatus;
    }

    public void setGliderStatus(GliderStatus gliderStatus) {
        this.gliderStatus = gliderStatus;
    }

    public LocalDate getManufacturingDate() {
        return manufacturingDate;
    }

    public void setManufacturingDate(LocalDate manufacturingDate) {
        this.manufacturingDate = manufacturingDate;
    }

    public Boolean getInUse() {
        return inUse;
    }

    public void setInUse(Boolean inUse) {
        this.inUse = inUse;
    }

    public String getDesignDeviation() {
        return designDeviation;
    }

    public void setDesignDeviation(String designDeviation) {
        this.designDeviation = designDeviation;
    }

    public String getDesignComplianceRecord() {
        return designComplianceRecord;
    }

    public void setDesignComplianceRecord(String designComplianceRecord) {
        this.designComplianceRecord = designComplianceRecord;
    }

    public Long getTotalFlightTimeInSeconds() {
        return totalFlightTimeInSeconds;
    }

    public void setTotalFlightTimeInSeconds(Long totalFlightTimeInSeconds) {
        this.totalFlightTimeInSeconds = totalFlightTimeInSeconds;
    }

    public Long getTotalFlightTimeSinceLastMaintenanceInSeconds() {
        return totalFlightTimeSinceLastMaintenanceInSeconds;
    }

    public void setTotalFlightTimeSinceLastMaintenanceInSeconds(Long totalFlightTimeSinceLastMaintenanceInSeconds) {
        this.totalFlightTimeSinceLastMaintenanceInSeconds = totalFlightTimeSinceLastMaintenanceInSeconds;
    }

    public SoftwareVersion getAutopilotSoftwareVersion() {
        return autopilotSoftwareVersion;
    }

    public void setAutopilotSoftwareVersion(SoftwareVersion autopilotSoftwareVersion) {
        this.autopilotSoftwareVersion = autopilotSoftwareVersion;
    }

    public SoftwareVersion getDesiredAutopilotSoftwareVersion() {
        return desiredAutopilotSoftwareVersion;
    }

    public void setDesiredAutopilotSoftwareVersion(SoftwareVersion desiredAutopilotSoftwareVersion) {
        this.desiredAutopilotSoftwareVersion = desiredAutopilotSoftwareVersion;
    }

    public SoftwareVersion getJetsonSoftwareVersion() {
        return jetsonSoftwareVersion;
    }

    public void setJetsonSoftwareVersion(SoftwareVersion jetsonSoftwareVersion) {
        this.jetsonSoftwareVersion = jetsonSoftwareVersion;
    }

    public SoftwareVersion getDesiredJetsonSoftwareVersion() {
        return desiredJetsonSoftwareVersion;
    }

    public void setDesiredJetsonSoftwareVersion(SoftwareVersion desiredJetsonSoftwareVersion) {
        this.desiredJetsonSoftwareVersion = desiredJetsonSoftwareVersion;
    }


    public SoftwareVersion getFtsPixhawkSoftwareVersion() {
        return ftsPixhawkSoftwareVersion;
    }

    public void setFtsPixhawkSoftwareVersion(SoftwareVersion ftsPixhawkSoftwareVersion) {
        this.ftsPixhawkSoftwareVersion = ftsPixhawkSoftwareVersion;
    }

    public SoftwareVersion getDesiredFtsPixhawkSoftwareVersion() {
        return desiredFtsPixhawkSoftwareVersion;
    }

    public void setDesiredFtsPixhawkSoftwareVersion(SoftwareVersion desiredFtsPixhawkSoftwareVersion) {
        this.desiredFtsPixhawkSoftwareVersion = desiredFtsPixhawkSoftwareVersion;
    }

    public SoftwareVersion getFtsRaspiSoftwareVersion() {
        return ftsRaspiSoftwareVersion;
    }

    public void setFtsRaspiSoftwareVersion(SoftwareVersion ftsRaspiSoftwareVersion) {
        this.ftsRaspiSoftwareVersion = ftsRaspiSoftwareVersion;
    }

    public SoftwareVersion getDesiredFtsRaspiSoftwareVersion() {
        return desiredFtsRaspiSoftwareVersion;
    }

    public void setDesiredFtsRaspiSoftwareVersion(SoftwareVersion desiredFtsRaspiSoftwareVersion) {
        this.desiredFtsRaspiSoftwareVersion = desiredFtsRaspiSoftwareVersion;
    }

    
}