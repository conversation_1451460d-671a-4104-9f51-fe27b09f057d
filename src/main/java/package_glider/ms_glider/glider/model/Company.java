package package_glider.ms_glider.glider.model;

import jakarta.persistence.*;
import lombok.EqualsAndHashCode;

@Entity
@Table(name = "company")
@EqualsAndHashCode(callSuper = true)
public final class Company extends Base {
    @Column(name = "name")
    private String name;

    public String getName() {
        return name;
    }
    public void setName(String name) {
        this.name = name;
    }
}

