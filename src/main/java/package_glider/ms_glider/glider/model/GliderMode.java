package package_glider.ms_glider.glider.model;

import jakarta.persistence.*;
import lombok.EqualsAndHashCode;

@Entity
@Table(name = "glider_mode")
@EqualsAndHashCode(callSuper = true)
public final class GliderMode extends Base {
    @Column(name = "name")
    private String name;
    @Column(name = "description")
    private String description;
    public String getName() {
        return name;
    }
    public void setName(String name) {
        this.name = name;
    }
    public String getDescription() {
        return description;
    }
    public void setDescription(String description) {
        this.description = description;
    }
}