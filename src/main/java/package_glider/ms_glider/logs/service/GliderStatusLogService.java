package package_glider.ms_glider.logs.service;
import java.util.List;
import java.util.Optional;
import java.util.stream.Collectors;
import java.util.stream.Stream;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;

import package_glider.ms_glider.logs.repository.GliderStatusLogRepository;
import package_glider.ms_glider.logs.model.GliderStatusLog;
import package_glider.ms_glider.logs.crud.GliderStatusLogRead;




@Component
@RequiredArgsConstructor
public class GliderStatusLogService {
    private final GliderStatusLogRepository gliderStatusLogRepository;

    public Optional<GliderStatusLog> findById(final Integer id) {
        return id == null ? Optional.empty() : gliderStatusLogRepository.findById(id);
    }
    
    @Transactional(readOnly = true)
    public List<GliderStatusLogRead> findByGliderName(final String gliderName) {
        return gliderStatusLogRepository.findByGliderName(gliderName)
        .map(GliderStatusLogRead::from)
        .collect(Collectors.toList());
    }

    public Stream<GliderStatusLog> list() {
        return gliderStatusLogRepository.findAll().stream();
    }

    public Optional<GliderStatusLog> add(final GliderStatusLog gliderStatusLog) {
        return Optional.of(gliderStatusLogRepository.save(gliderStatusLog));
    }
}