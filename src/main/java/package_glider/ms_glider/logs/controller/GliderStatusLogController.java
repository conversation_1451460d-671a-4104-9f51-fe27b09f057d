package package_glider.ms_glider.logs.controller;

import io.swagger.v3.oas.annotations.Operation;

import org.springframework.http.HttpStatus;
import org.springframework.validation.annotation.Validated;
import java.util.List;
import java.util.stream.Stream;
import lombok.RequiredArgsConstructor;
import org.springframework.web.bind.annotation.CrossOrigin;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.ResponseStatus;
import org.springframework.web.bind.annotation.RestController;
import package_glider.ms_glider.live_glider_data.model.GliderStatus;
import package_glider.ms_glider.live_glider_data.service.GliderStatusService;
import package_glider.ms_glider.logs.crud.GliderStatusLogCreate;
import package_glider.ms_glider.logs.crud.GliderStatusLogRead;
import package_glider.ms_glider.logs.service.GliderStatusLogService;



@ResponseStatus(HttpStatus.NOT_FOUND)
class NotFound extends RuntimeException {
}


@RestController
@RequestMapping("/api/logs/glider-statuses")
@CrossOrigin("*")
@RequiredArgsConstructor
public class GliderStatusLogController {
    private final GliderStatusLogService gliderStatusLogService;
    private final GliderStatusService gliderStatusService;

    
    @Operation(summary = "Get a glider status log entry by id")
    @GetMapping("/{id}")
    GliderStatusLogRead result(final @PathVariable Integer id) {
        return gliderStatusLogService.findById(id).map(item -> GliderStatusLogRead.from(item))
                .orElseThrow(() -> new NotFound());
    }
    
    
    @Operation(summary = "List all glider status logs")
    @GetMapping
    Stream<GliderStatusLogRead> all() {
        return gliderStatusLogService.list().map(item -> GliderStatusLogRead.from(item));
    }

    @Operation(summary = "List all glider status logs for a given glider name")
    @GetMapping("/glider-name/{gliderName}")
    List<GliderStatusLogRead> readByGliderName(final @PathVariable String gliderName) {
        return gliderStatusLogService.findByGliderName(gliderName);
    }

    @Operation(summary = "Add a new glider status log entry")
    @PostMapping
    GliderStatusLogRead add(final @RequestBody @Validated GliderStatusLogCreate newLog) {
        GliderStatusLogRead res = gliderStatusLogService.add(GliderStatusLogCreate.to(newLog, gliderStatus(newLog.getNewGliderStatusId()), gliderStatus(newLog.getOldGliderStatusId()))).map(GliderStatusLogRead::from).orElseThrow(NotFound::new);
        return res;
    }

    private GliderStatus gliderStatus(final Integer gliderStatusId) {
        return gliderStatusService.findById(gliderStatusId).orElse(null);
    }
    
}