package package_glider.ms_glider.logs.model;

import jakarta.persistence.*;
import lombok.EqualsAndHashCode;

import package_glider.ms_glider.live_glider_data.model.GliderStatus;

@Entity
@Table(name = "glider_status_logs")
@EqualsAndHashCode(callSuper = true)
public final class GliderStatusLog extends Base {
    @Column(name = "glider_name")
    String gliderName;

    @Column(name = "user_email", nullable = true)
    String userEmail;
    
    @Column(name = "note", nullable = true)
    String note;

    @ManyToOne
    @JoinColumn(name = "new_glider_status_id", nullable = true)
    GliderStatus newGliderStatus;

    @ManyToOne
    @JoinColumn(name = "old_glider_status_id", nullable = true)
    GliderStatus oldGliderStatus;

    public String getGliderName() {
        return gliderName;
    }

    public void setGliderName(String gliderName) {
        this.gliderName = gliderName;
    }

    public String getUserEmail() {
        return userEmail;
    }

    public void setUserEmail(String userEmail) {
        this.userEmail = userEmail;
    }

    public String getNote() {
        return note;
    }

    public void setNote(String note) {
        this.note = note;
    }

    public GliderStatus getNewGliderStatus() {
        return newGliderStatus;
    }

    public void setNewGliderStatus(GliderStatus newGliderStatus) {
        this.newGliderStatus = newGliderStatus;
    }

    public GliderStatus getOldGliderStatus() {
        return oldGliderStatus;
    }

    public void setOldGliderStatus(GliderStatus oldGliderStatus) {
        this.oldGliderStatus = oldGliderStatus;
    }


    
    
}