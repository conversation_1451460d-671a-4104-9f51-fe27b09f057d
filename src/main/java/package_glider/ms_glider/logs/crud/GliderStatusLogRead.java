package package_glider.ms_glider.logs.crud;

import java.time.LocalDateTime;

import package_glider.ms_glider.live_glider_data.model.GliderStatus;
import package_glider.ms_glider.logs.model.GliderStatusLog;





public class GliderStatusLogRead {
    private Integer id;
    private LocalDateTime createdAt;
    private LocalDateTime updatedAt;
    private String gliderName;
    private String userEmail;
    private String note;
    private GliderStatus newGliderStatus;
    private GliderStatus oldGliderStatus;
    

    public static GliderStatusLogRead from(final GliderStatusLog item) {
        final GliderStatusLogRead gliderStatusLog = new GliderStatusLogRead();
        gliderStatusLog.setId(item.getId());
        gliderStatusLog.setCreatedAt(item.getCreatedAt());
        gliderStatusLog.setUpdatedAt(item.getUpdatedAt());
        gliderStatusLog.setGliderName(item.getGliderName());
        gliderStatusLog.setUserEmail(item.getUserEmail());
        gliderStatusLog.setNote(item.getNote());
        gliderStatusLog.setNewGliderStatus(item.getNewGliderStatus());
        gliderStatusLog.setOldGliderStatus(item.getOldGliderStatus());
        
        return gliderStatusLog;
    }


    public Integer getId() {
        return id;
    }


    public void setId(Integer id) {
        this.id = id;
    }


    public LocalDateTime getCreatedAt() {
        return createdAt;
    }


    public void setCreatedAt(LocalDateTime createdAt) {
        this.createdAt = createdAt;
    }


    public LocalDateTime getUpdatedAt() {
        return updatedAt;
    }


    public void setUpdatedAt(LocalDateTime updatedAt) {
        this.updatedAt = updatedAt;
    }


    public String getGliderName() {
        return gliderName;
    }


    public void setGliderName(String gliderName) {
        this.gliderName = gliderName;
    }


    public String getUserEmail() {
        return userEmail;
    }


    public void setUserEmail(String userEmail) {
        this.userEmail = userEmail;
    }


    public String getNote() {
        return note;
    }


    public void setNote(String note) {
        this.note = note;
    }


    public GliderStatus getNewGliderStatus() {
        return newGliderStatus;
    }


    public void setNewGliderStatus(GliderStatus newGliderStatus) {
        this.newGliderStatus = newGliderStatus;
    }


    public GliderStatus getOldGliderStatus() {
        return oldGliderStatus;
    }


    public void setOldGliderStatus(GliderStatus oldGliderStatus) {
        this.oldGliderStatus = oldGliderStatus;
    }

    
}