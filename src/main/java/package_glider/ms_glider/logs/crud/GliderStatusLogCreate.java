package package_glider.ms_glider.logs.crud;

import package_glider.ms_glider.live_glider_data.model.GliderStatus;
import package_glider.ms_glider.logs.model.GliderStatusLog;



public class GliderStatusLogCreate {
    private String gliderName;
    private String userEmail;
    private String note;
    private Integer newGliderStatusId;
    private Integer oldGliderStatusId;


    public static GliderStatusLog to(final GliderStatusLogCreate item, final GliderStatus newGliderStatus, final GliderStatus oldGliderStatus) {
        final GliderStatusLog res = new GliderStatusLog();
        
        res.setGliderName(item.gliderName);
        res.setUserEmail(item.userEmail);
        res.setNote(item.note);
        res.setNewGliderStatus(newGliderStatus);
        res.setOldGliderStatus(oldGliderStatus);

        return res;
    }


    public String getGliderName() {
        return gliderName;
    }


    public void setGliderName(String gliderName) {
        this.gliderName = gliderName;
    }


    public String getUserEmail() {
        return userEmail;
    }


    public void setUserEmail(String userEmail) {
        this.userEmail = userEmail;
    }


    public String getNote() {
        return note;
    }


    public void setNote(String note) {
        this.note = note;
    }


    public Integer getNewGliderStatusId() {
        return newGliderStatusId;
    }


    public void setNewGliderStatusId(Integer newGliderStatusId) {
        this.newGliderStatusId = newGliderStatusId;
    }


    public Integer getOldGliderStatusId() {
        return oldGliderStatusId;
    }


    public void setOldGliderStatusId(Integer oldGliderStatusId) {
        this.oldGliderStatusId = oldGliderStatusId;
    }


    
    
}