package package_glider.ms_glider.maintenance.controller;

import io.swagger.v3.oas.annotations.Operation;

import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.http.HttpStatus;
import java.time.LocalDateTime;
import java.util.Arrays;
import java.util.List;
import java.util.stream.Stream;
import lombok.RequiredArgsConstructor;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.CrossOrigin;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PatchMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.ResponseStatus;
import org.springframework.web.bind.annotation.RestController;
import package_glider.ms_glider.glider.model.Glider;
import package_glider.ms_glider.glider.service.GliderService;
import package_glider.ms_glider.mailbox.model.Mailbox;
import package_glider.ms_glider.mailbox.service.MailboxService;
import package_glider.ms_glider.maintenance.crud.MaintenanceCreate;
import package_glider.ms_glider.maintenance.crud.MaintenanceRead;
import package_glider.ms_glider.maintenance.crud.MaintenanceUpdate;
import package_glider.ms_glider.maintenance.model.MaintenanceType;
import package_glider.ms_glider.maintenance.service.MaintenanceService;
import package_glider.ms_glider.maintenance.service.MaintenanceTypeService;
import package_glider.ms_glider.license_record.crud.LicenseRecordRead;
import package_glider.ms_glider.license_record.service.LicenseRecordService;



@ResponseStatus(HttpStatus.NOT_FOUND)
class NotFound extends RuntimeException {
}

@ResponseStatus(HttpStatus.BAD_REQUEST)
class BadRequestException extends RuntimeException {
}

@ResponseStatus(HttpStatus.FORBIDDEN)
class ForbiddenException extends RuntimeException {
}

@RestController
@RequestMapping("/api/maintenances")
@CrossOrigin("*")
@RequiredArgsConstructor
public class MaintenanceController {
    private final MaintenanceService maintenanceService;
    private final GliderService gliderService;
    private final MailboxService mailboxService;
    private final MaintenanceTypeService maintenanceTypeService;
    private final LicenseRecordService licenseRecordService;

    
    @Operation(summary = "Get a maintenance by id")
    @GetMapping("/{id}")
    MaintenanceRead result(final @PathVariable Integer id) {
        return maintenanceService.findById(id).map(item -> MaintenanceRead.from(item))
                .orElseThrow(() -> new NotFound());
    }
    
    @Operation(summary = "Get a glider maintenance by glider id")
    @GetMapping("/glider/{gliderId}")
    List<MaintenanceRead> resultByGliderId(final @PathVariable Integer gliderId) {
        return maintenanceService.findByGliderId(gliderId);
    }

    @Operation(summary = "List maintenance history")
    @GetMapping
    Stream<MaintenanceRead> all(@RequestParam(defaultValue = "0") int skip,
                               @RequestParam(defaultValue = "10") int limit) {
        return maintenanceService.list()
                .skip(skip)
                .limit(limit)
                .map(item -> MaintenanceRead.from(item));
    }

    @Operation(summary = "Update a maintenance by id")
    @PatchMapping("/{id}")
    MaintenanceRead updateGlider(final @PathVariable Integer id, final @RequestBody @Validated MaintenanceUpdate request) {
        return maintenanceService.updateMaintenance(id, request, glider(request.getGliderId()), mailbox(request.getMailboxId()), maintenanceType(request.getMaintenanceTypeId())).map(item -> MaintenanceRead.from(item)).orElseThrow(() -> new NotFound());
    }

    // TODO: Chek why mailbox doesn't work
    @Operation(summary = "Register a new maintenance record")
    @PostMapping
    MaintenanceRead add(final @RequestBody @Validated MaintenanceCreate newMaintenance) {
        if ((newMaintenance.getGliderId() == null && newMaintenance.getMailboxId() == null) ||
            (newMaintenance.getGliderId() != null && newMaintenance.getMailboxId() != null)) {
            throw new BadRequestException();
        }
        
        // Get the line+generation of the glider if the maintenance is for a glider
        String gliderLineGen = null;
        if (newMaintenance.getGliderId() != null) {
            Glider glider = glider(newMaintenance.getGliderId());
            gliderLineGen = glider.getLine() + glider.getGeneration();
            // Transform the glider line+generation to lowercase
            gliderLineGen = gliderLineGen.toLowerCase();
        }

        // Get the mailbox name if the maintenance is for a mailbox
        String mailboxName = null;
        if (newMaintenance.getMailboxId() != null) {
            Mailbox mailbox = mailbox(newMaintenance.getMailboxId());
            mailboxName = mailbox.getName();
            // Transform the mailbox name to lowercase
            mailboxName = mailboxName.toLowerCase();
        }

        // Check that the client is authorized to add a maintenance record
        boolean isAuthorized = false;

        // Find all license records where studentName is the same as the maintenanceStaff
        List<LicenseRecordRead> license_records = licenseRecordService.findAllByStudentEmail(newMaintenance.getMaintenanceStaff());
        if (license_records.isEmpty()) {
            System.err.println("The maintenance staff does not have any license records");
            throw new ForbiddenException();    
        }
        for (LicenseRecordRead license_record : license_records) {
            // Check if the license expiration date is in the future
            // Convert the String to a LocalDateTime
            LocalDateTime expirationDate = null;
            try {
                expirationDate = LocalDateTime.parse(license_record.getLicenseExpiryDate());
            } catch (Exception e) {
                // Failed to parse date, leaving as null
            }
            System.out.println(expirationDate);
            if (expirationDate != null && expirationDate.isAfter(LocalDateTime.now())) {
                continue;
            }

            // Check if the qualifications include "maintenance"
            List<String> qualifications = Arrays.asList(license_record.getQualifications().split(";"));
            System.out.println(qualifications);
            if (!qualifications.contains("maintenance")) {
                continue;
            }
            

            // Check if the license_record.getUaModel() string includes glider line+generation if the maintenance is for a glider
            if (newMaintenance.getGliderId() != null) {
                List<String> uaModels = Arrays.asList(license_record.getUaModel().split(";"));
                // transform all the glider line+generation to lowercase
                for (int i = 0; i < uaModels.size(); i++) {
                    uaModels.set(i, uaModels.get(i).toLowerCase());
                }
                if (uaModels.contains(gliderLineGen)) {
                    isAuthorized = true;
                    break;
                }
            }

            // Check if the license_record.getMailboxModel() string includes mailbox name if the maintenance is for a mailbox
            if (newMaintenance.getMailboxId() != null) {
                List<String> mailboxModels = Arrays.asList(license_record.getMailboxModel().split(";"));
                // transform all the mailbox names to lowercase
                for (int i = 0; i < mailboxModels.size(); i++) {
                    mailboxModels.set(i, mailboxModels.get(i).toLowerCase());
                }
                if (mailboxModels.contains(mailboxName)) {
                    isAuthorized = true;
                    break;
                }
            }
        }

        if (!isAuthorized) {
            System.err.println("The maintenance staff is not authorized to add a maintenance record");
            throw new ForbiddenException();   
        }
        
        try {
            MaintenanceRead res =  maintenanceService.add(MaintenanceCreate.to(newMaintenance, 
                    newMaintenance.getGliderId() != null ? glider(newMaintenance.getGliderId()) : null, 
                    newMaintenance.getMailboxId() != null ? mailbox(newMaintenance.getMailboxId()) : null, 
                    maintenanceType(newMaintenance.getMaintenanceTypeId())))
                    .map(MaintenanceRead::from)
                    .orElseThrow(NotFound::new);
            if (newMaintenance.getGliderId() != null) {
                if (newMaintenance.getIsScheduled()) {
                    gliderService.resetTotalFlightTimeSinceLastMaintenanceInSeconds(newMaintenance.getGliderId());
                }
                gliderService.updateGliderStatus("Post Maintenance Checks", glider(newMaintenance.getGliderId()).getName(), "Post maintenance checks", newMaintenance.getMaintenanceStaff());
            }
            return res;
        }
        catch (Exception e) {
            System.err.println(e);
            throw new BadRequestException();   
        }
    }

    private Glider glider(final Integer gliderID) {
        return gliderService.findById(gliderID).orElse(null);
    }

    private Mailbox mailbox(final Integer mailboxID) {
        return mailboxService.findById(mailboxID).orElse(null);
    }

    private MaintenanceType maintenanceType(final Integer maintenanceTypeID) {
        return maintenanceTypeService.findById(maintenanceTypeID).orElse(null);
    }
}