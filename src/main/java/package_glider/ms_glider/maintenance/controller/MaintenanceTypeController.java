package package_glider.ms_glider.maintenance.controller;

import io.swagger.v3.oas.annotations.Operation;
import java.util.stream.Stream;
import lombok.RequiredArgsConstructor;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.CrossOrigin;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import package_glider.ms_glider.maintenance.crud.MaintenanceTypeCreate;
import package_glider.ms_glider.maintenance.crud.MaintenanceTypeRead;
import package_glider.ms_glider.maintenance.service.MaintenanceTypeService;



@RestController
@RequestMapping("/api/maintenance-types")
@CrossOrigin("*")
@RequiredArgsConstructor
public class MaintenanceTypeController {
    private final MaintenanceTypeService maintenanceTypeService;
    
    @Operation(summary = "Get a maintenance type by id")
    @GetMapping("/{id}")
    MaintenanceTypeRead result(final @PathVariable Integer id) {
        return maintenanceTypeService.findById(id).map(item -> MaintenanceTypeRead.from(item))
                .orElseThrow(() -> new NotFound());
    }

    @Operation(summary = "List maintenance types")
    @GetMapping
    Stream<MaintenanceTypeRead> all() {
        return maintenanceTypeService.list().map(item -> MaintenanceTypeRead.from(item));
    }

    @Operation(summary = "Register a new maintenance type")
    @PostMapping
    MaintenanceTypeRead add(final @RequestBody @Validated MaintenanceTypeCreate newMaintenanceType) {
        return maintenanceTypeService.add(MaintenanceTypeCreate.to(newMaintenanceType))
                .map(item -> MaintenanceTypeRead.from(item)).orElseThrow(() -> new NotFound());
    }
}