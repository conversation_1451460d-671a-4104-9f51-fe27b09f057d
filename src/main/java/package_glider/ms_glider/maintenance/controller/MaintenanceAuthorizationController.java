package package_glider.ms_glider.maintenance.controller;

import io.swagger.v3.oas.annotations.Operation;
import java.util.List;
import java.util.UUID;
import java.util.stream.Stream;
import lombok.RequiredArgsConstructor;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.CrossOrigin;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import package_glider.ms_glider.maintenance.crud.MaintenanceAuthorizationCreate;
import package_glider.ms_glider.maintenance.crud.MaintenanceAuthorizationRead;
import package_glider.ms_glider.maintenance.service.MaintenanceAuthorizationService;





@RestController
@RequestMapping("/api/maintenance-authorizations")
@CrossOrigin("*")
@RequiredArgsConstructor
public class MaintenanceAuthorizationController {
    private final MaintenanceAuthorizationService maintenanceAuthorizationService;


    
    @Operation(summary = "Get user maintenance permissions by user id (UUID)")
    @GetMapping("/user-id/{userId}")
    List<MaintenanceAuthorizationRead> userPermissions(final @PathVariable UUID userId) {
        return maintenanceAuthorizationService.findByUserId(userId);
    }
    

    @Operation(summary = "List all maintenance authorizations")
    @GetMapping
    Stream<MaintenanceAuthorizationRead> all() {
        return maintenanceAuthorizationService.list().map(item -> MaintenanceAuthorizationRead.from(item));
    }

    @Operation(summary = "Register a new maintenance authorization")
    @PostMapping
    MaintenanceAuthorizationRead add(final @RequestBody @Validated MaintenanceAuthorizationCreate maintenanceAuthorization) {
        return maintenanceAuthorizationService.add(MaintenanceAuthorizationCreate.to(maintenanceAuthorization))
                .map(item -> MaintenanceAuthorizationRead.from(item)).orElseThrow(() -> new NotFound());
    }
}