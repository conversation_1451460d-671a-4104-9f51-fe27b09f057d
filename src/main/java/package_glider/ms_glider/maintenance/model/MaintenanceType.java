package package_glider.ms_glider.maintenance.model;

import jakarta.persistence.*;
import lombok.EqualsAndHashCode;

@Entity
@Table(name = "maintenance_type")
@EqualsAndHashCode(callSuper = true)
public final class MaintenanceType extends Base {
    @Column(name = "name")
    private String name;
    public String getName() {
        return name;
    }
    public void setName(String name) {
        this.name = name;
    }
}