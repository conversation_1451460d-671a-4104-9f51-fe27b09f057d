package package_glider.ms_glider.maintenance.model;

import jakarta.persistence.*;
import java.time.LocalDateTime;
import lombok.EqualsAndHashCode;
import package_glider.ms_glider.glider.model.Glider;
import package_glider.ms_glider.mailbox.model.Mailbox;


@Entity
@Table(name = "maintenance")
@EqualsAndHashCode(callSuper = true)
public final class Maintenance extends Base {
    
    @Column(name = "due_date_time")
    private LocalDateTime dueDateTime;
    @Column(name = "completed_date_time")
    private LocalDateTime completedDateTime;
    @Column(name = "is_scheduled")
    private Boolean isScheduled;
    @Column(name = "notes", length = 65536)
    private String notes;
    @Column(name = "maintenance_staff", length = 65536)
    private String maintenanceStaff;
    @Column(name = "post_maintenance_checklist_done")
    private Boolean postMaintenanceChecklistDone;

    @ManyToOne
    @JoinColumn(name = "glider_id", nullable = true)
    private Glider glider;

    @ManyToOne
    @JoinColumn(name = "mailbox_id", nullable = true)
    private Mailbox mailbox;

    @ManyToOne
    @JoinColumn(name = "maintenance_type_id", nullable = false)
    private MaintenanceType maintenanceType;

    public LocalDateTime getDueDateTime() {
        return dueDateTime;
    }

    public void setDueDateTime(LocalDateTime dueDateTime) {
        this.dueDateTime = dueDateTime;
    }

    public LocalDateTime getCompletedDateTime() {
        return completedDateTime;
    }

    public void setCompletedDateTime(LocalDateTime completedDateTime) {
        this.completedDateTime = completedDateTime;
    }

    public Boolean getIsScheduled() {
        return isScheduled;
    }

    public void setIsScheduled(Boolean isScheduled) {
        this.isScheduled = isScheduled;
    }

    public String getNotes() {
        return notes;
    }

    public void setNotes(String notes) {
        this.notes = notes;
    }

    public Glider getGlider() {
        return glider;
    }

    public void setGlider(Glider glider) {
        this.glider = glider;
    }

    public Mailbox getMailbox() {
        return mailbox;
    }

    public void setMailbox(Mailbox mailbox) {
        this.mailbox = mailbox;
    }

    public MaintenanceType getMaintenanceType() {
        return maintenanceType;
    }

    public void setMaintenanceType(MaintenanceType maintenanceType) {
        this.maintenanceType = maintenanceType;
    }

    public String getMaintenanceStaff() {
        return maintenanceStaff;
    }

    public void setMaintenanceStaff(String maintenanceStaff) {
        this.maintenanceStaff = maintenanceStaff;
    }

    public Boolean getPostMaintenanceChecklistDone() {
        return postMaintenanceChecklistDone;
    }

    public void setPostMaintenanceChecklistDone(Boolean postMaintenanceChecklistDone) {
        this.postMaintenanceChecklistDone = postMaintenanceChecklistDone;
    }

    
    
}