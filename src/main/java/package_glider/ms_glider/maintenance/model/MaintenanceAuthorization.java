package package_glider.ms_glider.maintenance.model;

import jakarta.persistence.*;
import java.util.UUID;
import lombok.EqualsAndHashCode;



@Entity
@Table(name = "maintenance_authorization")
@EqualsAndHashCode(callSuper = true)
public final class MaintenanceAuthorization extends Base {
    
    @Column(name = "user_id")
    private UUID userId;
    
    @Column(name = "user_email")
    private String userEmail;

    @Column(name = "glider_type")
    private String gliderType;

    public UUID getUserId() {
      return userId;
    }

    public void setUserId(UUID userId) {
      this.userId = userId;
    }

    public String getUserEmail() {
      return userEmail;
    }

    public void setUserEmail(String userEmail) {
      this.userEmail = userEmail;
    }

    public String getGliderType() {
      return gliderType;
    }

    public void setGliderType(String gliderType) {
      this.gliderType = gliderType;
    }

    
}