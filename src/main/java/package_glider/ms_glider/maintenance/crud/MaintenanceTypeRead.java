package package_glider.ms_glider.maintenance.crud;

import package_glider.ms_glider.maintenance.model.MaintenanceType;


public class MaintenanceTypeRead {
    private Integer id;
    private String name;
    

    public static MaintenanceTypeRead from(final MaintenanceType item) {
        final MaintenanceTypeRead maintenanceType = new MaintenanceTypeRead();
        maintenanceType.setId(item.getId());
        maintenanceType.setName(item.getName());

        return maintenanceType;
    }


    public Integer getId() {
        return id;
    }


    public void setId(Integer id) {
        this.id = id;
    }


    public String getName() {
      return name;
    }


    public void setName(String name) {
      this.name = name;
    }


  
    
}