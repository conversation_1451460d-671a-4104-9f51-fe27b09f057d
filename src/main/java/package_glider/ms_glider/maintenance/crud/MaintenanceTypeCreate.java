package package_glider.ms_glider.maintenance.crud;
import package_glider.ms_glider.maintenance.model.MaintenanceType;


public class MaintenanceTypeCreate {
    private String name;

    public static MaintenanceType to(final MaintenanceTypeCreate item) {
        final MaintenanceType res = new MaintenanceType();
        
        res.setName(item.getName());

        return res;
    }

    public String getName() {
      return name;
    }

    public void setName(String name) {
      this.name = name;
    }
  
    
}