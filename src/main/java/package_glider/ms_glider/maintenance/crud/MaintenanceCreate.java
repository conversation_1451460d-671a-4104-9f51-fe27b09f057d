package package_glider.ms_glider.maintenance.crud;

import java.time.LocalDateTime;
import package_glider.ms_glider.glider.model.Glider;
import package_glider.ms_glider.mailbox.model.Mailbox;
import package_glider.ms_glider.maintenance.model.Maintenance;
import package_glider.ms_glider.maintenance.model.MaintenanceType;




public class MaintenanceCreate {
    private LocalDateTime dueDateTime;
    private LocalDateTime completedDateTime;
    private Boolean isScheduled;
    private String notes;
    private String maintenanceStaff;
    private Boolean postMaintenanceChecklistDone;
    private Integer gliderId;
    private Integer mailboxId;
    private Integer maintenanceTypeId;


    public static Maintenance to(final MaintenanceCreate item, final Glider glider, final Mailbox mailbox, final MaintenanceType maintenanceType) {
        final Maintenance res = new Maintenance();
        
        res.setDueDateTime(item.getDueDateTime());
        res.setCompletedDateTime(item.getCompletedDateTime());
        res.setIsScheduled(item.getIsScheduled());
        res.setNotes(item.getNotes());
        res.setMaintenanceStaff(item.getMaintenanceStaff());
        res.setPostMaintenanceChecklistDone(item.getPostMaintenanceChecklistDone());
        res.setGlider(glider);
        res.setMailbox(mailbox);
        res.setMaintenanceType(maintenanceType);

        return res;
    }


    public LocalDateTime getDueDateTime() {
        return dueDateTime;
    }


    public void setDueDate(LocalDateTime dueDateTime) {
        this.dueDateTime = dueDateTime;
    }


    public LocalDateTime getCompletedDateTime() {
        return completedDateTime;
    }


    public void setCompletedDateTime(LocalDateTime completedDateTime) {
        this.completedDateTime = completedDateTime;
    }


    public Boolean getIsScheduled() {
        return isScheduled;
    }


    public void setIsScheduled(Boolean isScheduled) {
        this.isScheduled = isScheduled;
    }


    public String getNotes() {
        return notes;
    }


    public void setNotes(String notes) {
        this.notes = notes;
    }


    public Integer getGliderId() {
        return gliderId;
    }


    public void setGliderId(Integer gliderId) {
        this.gliderId = gliderId;
    }


    public Integer getMailboxId() {
        return mailboxId;
    }


    public void setMailboxId(Integer mailboxId) {
        this.mailboxId = mailboxId;
    }


    public Integer getMaintenanceTypeId() {
        return maintenanceTypeId;
    }


    public void setMaintenanceTypeId(Integer maintenanceTypeId) {
        this.maintenanceTypeId = maintenanceTypeId;
    }


    public String getMaintenanceStaff() {
        return maintenanceStaff;
    }


    public void setMaintenanceStaff(String maintenanceStaff) {
        this.maintenanceStaff = maintenanceStaff;
    }


    public Boolean getPostMaintenanceChecklistDone() {
        return postMaintenanceChecklistDone;
    }


    public void setPostMaintenanceChecklistDone(Boolean postMaintenanceChecklistDone) {
        this.postMaintenanceChecklistDone = postMaintenanceChecklistDone;
    }

    
    
    
}