package package_glider.ms_glider.maintenance.crud;

import java.time.LocalDateTime;

public class MaintenanceUpdate {
    private LocalDateTime dueDateTime;
    private LocalDateTime completedDateTime;
    private String notes;
    private Boolean postMaintenanceChecklistDone;
    private Integer gliderId;
    private Integer maintenanceTypeId;
    private Integer mailboxId;
    private String maintenanceStaff;


    public LocalDateTime getDueDateTime() {
        return dueDateTime;
    }
    public void setDueDateTime(LocalDateTime dueDateTime) {
        this.dueDateTime = dueDateTime;
    }
    public LocalDateTime getCompletedDateTime() {
        return completedDateTime;
    }
    public void setCompletedDateTime(LocalDateTime completedDateTime) {
        this.completedDateTime = completedDateTime;
    }
    public String getNotes() {
        return notes;
    }
    public void setNotes(String notes) {
        this.notes = notes;
    }
    public Boolean getPostMaintenanceChecklistDone() {
        return postMaintenanceChecklistDone;
    }
    public void setPostMaintenanceChecklistDone(Boolean postMaintenanceChecklistDone) {
        this.postMaintenanceChecklistDone = postMaintenanceChecklistDone;
    }
    public Integer getGliderId() {
        return gliderId;
    }
    public void setGliderId(Integer gliderId) {
        this.gliderId = gliderId;
    }
    public Integer getMaintenanceTypeId() {
        return maintenanceTypeId;
    }
    public void setMaintenanceTypeId(Integer maintenanceTypeId) {
        this.maintenanceTypeId = maintenanceTypeId;
    }
    public Integer getMailboxId() {
        return mailboxId;
    }
    public void setMailboxId(Integer mailboxId) {
        this.mailboxId = mailboxId;
    }
    public String getMaintenanceStaff() {
        return maintenanceStaff;
    }
    public void setMaintenanceStaff(String maintenanceStaff) {
        this.maintenanceStaff = maintenanceStaff;
    }

    
}