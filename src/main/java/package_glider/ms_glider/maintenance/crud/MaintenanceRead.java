package package_glider.ms_glider.maintenance.crud;

import java.time.LocalDateTime;
import package_glider.ms_glider.glider.model.Glider;
import package_glider.ms_glider.mailbox.model.Mailbox;
import package_glider.ms_glider.maintenance.model.Maintenance;
import package_glider.ms_glider.maintenance.model.MaintenanceType;




public class MaintenanceRead {
    private Integer id;
    private LocalDateTime dueDateTime;
    private LocalDateTime completedDateTime;
    private Boolean isScheduled;
    private String notes;
    private String maintenanceStaff;
    private Boolean postMaintenanceChecklistDone;
    private Glider glider;
    private Mailbox mailbox;
    private MaintenanceType maintenanceType;
    

    public static MaintenanceRead from(final Maintenance item) {
        final MaintenanceRead gliderMaintenance = new MaintenanceRead();
        gliderMaintenance.setId(item.getId());
        gliderMaintenance.setDueDateTime(item.getDueDateTime());
        gliderMaintenance.setCompletedDateTime(item.getCompletedDateTime());
        gliderMaintenance.setIsScheduled(item.getIsScheduled());
        gliderMaintenance.setNotes(item.getNotes());
        gliderMaintenance.setMaintenanceStaff(item.getMaintenanceStaff());
        gliderMaintenance.setPostMaintenanceChecklistDone(item.getPostMaintenanceChecklistDone());
        gliderMaintenance.setGlider(item.getGlider());
        gliderMaintenance.setMailbox(item.getMailbox());
        gliderMaintenance.setMaintenanceType(item.getMaintenanceType());
        return gliderMaintenance;
    }


    public Integer getId() {
        return id;
    }


    public void setId(Integer id) {
        this.id = id;
    }


    public LocalDateTime getDueDateTime() {
        return dueDateTime;
    }


    public void setDueDateTime(LocalDateTime dueDateTime) {
        this.dueDateTime = dueDateTime;
    }


    public LocalDateTime getCompletedDateTime() {
        return completedDateTime;
    }


    public void setCompletedDateTime(LocalDateTime completedDateTime) {
        this.completedDateTime = completedDateTime;
    }


    public Boolean getIsScheduled() {
        return isScheduled;
    }


    public void setIsScheduled(Boolean isScheduled) {
        this.isScheduled = isScheduled;
    }


    public String getNotes() {
        return notes;
    }


    public void setNotes(String notes) {
        this.notes = notes;
    }


    public Glider getGlider() {
        return glider;
    }


    public void setGlider(Glider glider) {
        this.glider = glider;
    }


    public Mailbox getMailbox() {
        return mailbox;
    }


    public void setMailbox(Mailbox mailbox) {
        this.mailbox = mailbox;
    }


    public MaintenanceType getMaintenanceType() {
        return maintenanceType;
    }


    public void setMaintenanceType(MaintenanceType maintenanceType) {
        this.maintenanceType = maintenanceType;
    }


    public String getMaintenanceStaff() {
        return maintenanceStaff;
    }


    public void setMaintenanceStaff(String maintenanceStaff) {
        this.maintenanceStaff = maintenanceStaff;
    }


    public Boolean getPostMaintenanceChecklistDone() {
        return postMaintenanceChecklistDone;
    }


    public void setPostMaintenanceChecklistDone(Boolean postMaintenanceChecklistDone) {
        this.postMaintenanceChecklistDone = postMaintenanceChecklistDone;
    }
    
    
    
}