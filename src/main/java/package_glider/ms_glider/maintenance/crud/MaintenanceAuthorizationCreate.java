package package_glider.ms_glider.maintenance.crud;

import java.util.UUID;
import package_glider.ms_glider.maintenance.model.MaintenanceAuthorization;


public class MaintenanceAuthorizationCreate {
    private UUID userId;
    private String userEmail;
    private String gliderType;

    public static MaintenanceAuthorization to(final MaintenanceAuthorizationCreate item) {
        final MaintenanceAuthorization res = new MaintenanceAuthorization();
        
        res.setUserId(item.getUserId());
        res.setUserEmail(item.getUserEmail());
        res.setGliderType(item.getGliderType());

        return res;
    }

    public UUID getUserId() {
      return userId;
    }

    public void setUserId(UUID userId) {
      this.userId = userId;
    }

    public String getUserEmail() {
      return userEmail;
    }

    public void setUserEmail(String userEmail) {
      this.userEmail = userEmail;
    }

    public String getGliderType() {
      return gliderType;
    }

    public void setGliderType(String gliderType) {
      this.gliderType = gliderType;
    }
    
}