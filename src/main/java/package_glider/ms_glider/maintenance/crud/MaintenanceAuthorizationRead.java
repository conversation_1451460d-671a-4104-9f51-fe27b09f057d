package package_glider.ms_glider.maintenance.crud;

import java.util.UUID;
import package_glider.ms_glider.maintenance.model.MaintenanceAuthorization;





public class MaintenanceAuthorizationRead {
    private Integer id;
    private UUID userId;
    private String userEmail;
    private String gliderType;
    

    public static MaintenanceAuthorizationRead from(final MaintenanceAuthorization item) {
        final MaintenanceAuthorizationRead maintenanceAuthorization = new MaintenanceAuthorizationRead();
        maintenanceAuthorization.setId(item.getId());
        maintenanceAuthorization.setUserId(item.getUserId());
        maintenanceAuthorization.setUserEmail(item.getUserEmail());
        maintenanceAuthorization.setGliderType(item.getGliderType());

        return maintenanceAuthorization;
    }


    public Integer getId() {
      return id;
    }


    public void setId(Integer id) {
      this.id = id;
    }


    public UUID getUserId() {
      return userId;
    }


    public void setUserId(UUID userId) {
      this.userId = userId;
    }


    public String getUserEmail() {
      return userEmail;
    }


    public void setUserEmail(String userEmail) {
      this.userEmail = userEmail;
    }


    public String getGliderType() {
      return gliderType;
    }


    public void setGliderType(String gliderType) {
      this.gliderType = gliderType;
    }

    
    
}