package package_glider.ms_glider.maintenance.repository;

import java.util.UUID;
import java.util.stream.Stream;
import org.springframework.data.jpa.repository.JpaRepository;
import package_glider.ms_glider.maintenance.model.MaintenanceAuthorization;




public interface MaintenanceAuthorizationRepository extends JpaRepository<MaintenanceAuthorization, Integer> {
    Stream<MaintenanceAuthorization> findByUserId(UUID userId);
}