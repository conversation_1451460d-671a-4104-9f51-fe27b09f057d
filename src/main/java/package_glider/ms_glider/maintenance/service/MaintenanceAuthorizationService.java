package package_glider.ms_glider.maintenance.service;

import java.util.List;
import java.util.Optional;
import java.util.UUID;
import java.util.stream.Collectors;
import java.util.stream.Stream;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;
import package_glider.ms_glider.maintenance.crud.MaintenanceAuthorizationRead;
import package_glider.ms_glider.maintenance.model.MaintenanceAuthorization;
import package_glider.ms_glider.maintenance.repository.MaintenanceAuthorizationRepository;




@Component
@RequiredArgsConstructor
public class MaintenanceAuthorizationService {
    private final MaintenanceAuthorizationRepository maintenanceAuthorizationRepository;


    public Optional<MaintenanceAuthorization> findById(final Integer id) {
        return maintenanceAuthorizationRepository.findById(id);
    }

    @Transactional(readOnly = true)
    public List<MaintenanceAuthorizationRead> findByUserId(UUID userId) {
        return maintenanceAuthorizationRepository.findByUserId(userId)
                .map(MaintenanceAuthorizationRead::from)
                .collect(Collectors.toList());
    }

    public Stream<MaintenanceAuthorization> list() {
        return maintenanceAuthorizationRepository.findAll().stream();
    }

    public Optional<MaintenanceAuthorization> add(final MaintenanceAuthorization maintenanceAuthorization) {
        final MaintenanceAuthorization savedMaintenanceAuthorization = maintenanceAuthorizationRepository.save(maintenanceAuthorization);
        return Optional.of(savedMaintenanceAuthorization);
    }
}