package package_glider.ms_glider.maintenance.service;

import java.util.List;
import java.util.Optional;
import java.util.stream.Collectors;
import java.util.stream.Stream;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;
import package_glider.ms_glider.glider.model.Glider;
import package_glider.ms_glider.maintenance.crud.MaintenanceRead;
import package_glider.ms_glider.maintenance.crud.MaintenanceUpdate;
import package_glider.ms_glider.maintenance.model.Maintenance;
import package_glider.ms_glider.maintenance.model.MaintenanceType;
import package_glider.ms_glider.mailbox.model.Mailbox;
import package_glider.ms_glider.maintenance.repository.MaintenanceRepository;


@Component
@RequiredArgsConstructor
public class MaintenanceService {
    private final MaintenanceRepository maintenanceRepository;


    public Optional<Maintenance> findById(final Integer id) {
        return maintenanceRepository.findById(id);
    }

    @Transactional(readOnly = true)
    public List<MaintenanceRead> findByGliderId(final Integer gliderId) {
        return maintenanceRepository.findByGliderId(gliderId)
        .map(MaintenanceRead::from)
        .collect(Collectors.toList());
    }

    public Stream<Maintenance> list() {
        return maintenanceRepository.findAll().stream()
                .sorted((m1, m2) -> m2.getId().compareTo(m1.getId()));
    }

    public Optional<Maintenance> add(final Maintenance newMaintenance) {
        final Maintenance savedGliderMaintenance = maintenanceRepository.save(newMaintenance);
        return Optional.of(savedGliderMaintenance);
    }

    public Optional<Maintenance> updateMaintenance(Integer id, MaintenanceUpdate request, Glider glider, Mailbox mailbox, MaintenanceType maintenanceType) {
        return maintenanceRepository.findById(id).map(maintenance -> {
            if (request.getDueDateTime() != null) {
                maintenance.setDueDateTime(request.getDueDateTime());
            }
            if (request.getCompletedDateTime() != null) {
                maintenance.setCompletedDateTime(request.getCompletedDateTime());
            }
            if (request.getNotes() != null) {
                maintenance.setNotes(request.getNotes());
            }
            if (request.getPostMaintenanceChecklistDone() != null) {
                maintenance.setPostMaintenanceChecklistDone(request.getPostMaintenanceChecklistDone());
            }
            if (glider != null) {
                maintenance.setGlider(glider);
            }
            if (mailbox != null) {
                maintenance.setMailbox(mailbox);
            }
            if (maintenanceType != null) {
                maintenance.setMaintenanceType(maintenanceType);
            }
            if (request.getMaintenanceStaff() != null) {
                maintenance.setMaintenanceStaff(request.getMaintenanceStaff());
            }
            
            return maintenanceRepository.save(maintenance);
        });
    }
}