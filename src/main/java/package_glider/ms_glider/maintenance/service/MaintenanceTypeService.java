package package_glider.ms_glider.maintenance.service;

import java.util.Optional;
import java.util.stream.Stream;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Component;
import package_glider.ms_glider.maintenance.model.MaintenanceType;
import package_glider.ms_glider.maintenance.repository.MaintenanceTypeRepository;



@Component
@RequiredArgsConstructor
public class MaintenanceTypeService {
    private final MaintenanceTypeRepository maintenanceTypeRepository;


    public Optional<MaintenanceType> findById(final Integer id) {
        return maintenanceTypeRepository.findById(id);
    }

    public Stream<MaintenanceType> list() {
        return maintenanceTypeRepository.findAll().stream();
    }

    public Optional<MaintenanceType> add(final MaintenanceType maintenanceType) {
        final MaintenanceType savedGliderMaintenance = maintenanceTypeRepository.save(maintenanceType);
        return Optional.of(savedGliderMaintenance);
    }
}