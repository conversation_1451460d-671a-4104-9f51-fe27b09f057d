package package_glider.ms_glider.software_version.model;

import jakarta.persistence.*;
import lombok.EqualsAndHashCode;

@Entity
@Table(name = "software_version_type")
@EqualsAndHashCode(callSuper = true)
public final class SoftwareVersionType extends Base {
    @Column(name = "name", nullable = false, unique = true)
    private String name;

    public String getName() {
        return name;
    }
    public void setName(String name) {
        this.name = name;
    }
}