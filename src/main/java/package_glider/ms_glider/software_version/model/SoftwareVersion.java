package package_glider.ms_glider.software_version.model;

import jakarta.persistence.*;
import lombok.EqualsAndHashCode;

@Entity
@Table(name = "software_version")
@EqualsAndHashCode(callSuper = true)
public final class SoftwareVersion extends Base {
    @Column(name = "name")
    private String name;
    @ManyToOne
    @JoinColumn(name = "software_version_type_id", nullable = true)
    private SoftwareVersionType softwareVersionType;

    public String getName() {
        return name;
    }
    public void setName(String name) {
        this.name = name;
    }
    public SoftwareVersionType getSoftwareVersionType() {
        return softwareVersionType;
    }
    public void setSoftwareVersionType(SoftwareVersionType softwareVersionType) {
        this.softwareVersionType = softwareVersionType;
    }
}