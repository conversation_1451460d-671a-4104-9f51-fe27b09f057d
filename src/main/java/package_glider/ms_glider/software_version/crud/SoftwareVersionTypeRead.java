package package_glider.ms_glider.software_version.crud;
import java.time.LocalDateTime;
import package_glider.ms_glider.software_version.model.SoftwareVersionType;

public class SoftwareVersionTypeRead {
    private Integer id;
    private String name;
    private LocalDateTime createdAt;
    private LocalDateTime updatedAt;

    public static SoftwareVersionTypeRead from(final SoftwareVersionType item) {
        final SoftwareVersionTypeRead softwareVersionType = new SoftwareVersionTypeRead();
        softwareVersionType.setId(item.getId());
        softwareVersionType.setName(item.getName());
        softwareVersionType.setCreatedAt(item.getCreatedAt());
        softwareVersionType.setUpdatedAt(item.getUpdatedAt());
        return softwareVersionType;
    }

    public Integer getId() {
        return id;
    }

    public void setId(Integer id) {
        this.id = id;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public LocalDateTime getCreatedAt() {
        return createdAt;
    }

    public void setCreatedAt(LocalDateTime createdAt) {
        this.createdAt = createdAt;
    }

    public LocalDateTime getUpdatedAt() {
        return updatedAt;
    }

    public void setUpdatedAt(LocalDateTime updatedAt) {
        this.updatedAt = updatedAt;
    }

    
}
