package package_glider.ms_glider.software_version.crud;
import java.time.LocalDateTime;
import package_glider.ms_glider.software_version.model.SoftwareVersionType;

public class SoftwareVersionTypeCreate {
    private String name;
    private LocalDateTime createdAt;
    private LocalDateTime updatedAt;

    public static SoftwareVersionType to(final SoftwareVersionTypeCreate item) {
        final SoftwareVersionType res = new SoftwareVersionType();
        res.setName(item.getName());
        res.setCreatedAt(item.getCreatedAt());
        res.setUpdatedAt(item.getUpdatedAt());

        return res;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public LocalDateTime getCreatedAt() {
        return createdAt;
    }

    public void setCreatedAt(LocalDateTime createdAt) {
        this.createdAt = createdAt;
    }

    public LocalDateTime getUpdatedAt() {
        return updatedAt;
    }

    public void setUpdatedAt(LocalDateTime updatedAt) {
        this.updatedAt = updatedAt;
    }
}
