package package_glider.ms_glider.software_version.crud;

import java.time.LocalDateTime;
import package_glider.ms_glider.software_version.model.SoftwareVersion;
import package_glider.ms_glider.software_version.model.SoftwareVersionType;

public class SoftwareVersionCreate {
    private String name;
    private Integer SoftwareVersionTypeId;
    private LocalDateTime createdAt;
    private LocalDateTime updatedAt;

    public static SoftwareVersion to(final SoftwareVersionCreate item, final SoftwareVersionType softwareVersionType) {
        final SoftwareVersion res = new SoftwareVersion();
        res.setName(item.getName());
        res.setSoftwareVersionType(softwareVersionType);
        res.setCreatedAt(item.getCreatedAt());
        res.setUpdatedAt(item.getUpdatedAt());

        return res;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public Integer getSoftwareVersionTypeId() {
        return SoftwareVersionTypeId;
    }

    public void setSoftwareVersionTypeId(Integer SoftwareVersionTypeId) {
        this.SoftwareVersionTypeId = SoftwareVersionTypeId;
    }

    public LocalDateTime getCreatedAt() {
        return createdAt;
    }

    public void setCreatedAt(LocalDateTime createdAt) {
        this.createdAt = createdAt;
    }

    public LocalDateTime getUpdatedAt() {
        return updatedAt;
    }

    public void setUpdatedAt(LocalDateTime updatedAt) {
        this.updatedAt = updatedAt;
    }    
}
