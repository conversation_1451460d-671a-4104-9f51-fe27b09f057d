package package_glider.ms_glider.software_version.crud;
import java.time.LocalDateTime;
import package_glider.ms_glider.software_version.model.SoftwareVersion;
import package_glider.ms_glider.software_version.model.SoftwareVersionType;

public class SoftwareVersionRead {
    private Integer id;
    private String name;
    private SoftwareVersionType softwareVersionType;
    private LocalDateTime createdAt;
    private LocalDateTime updatedAt;

    public static SoftwareVersionRead from(final SoftwareVersion item) {
        final SoftwareVersionRead softwareVersionRead = new SoftwareVersionRead();
        softwareVersionRead.setId(item.getId());
        softwareVersionRead.setName(item.getName());
        softwareVersionRead.setSoftwareVersionType(item.getSoftwareVersionType());
        softwareVersionRead.setCreatedAt(item.getCreatedAt());
        softwareVersionRead.setUpdatedAt(item.getUpdatedAt());
        return softwareVersionRead;
    }

    public Integer getId() {
        return id;
    }

    public void setId(Integer id) {
        this.id = id;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public SoftwareVersionType getSoftwareVersionType() {
        return softwareVersionType;
    }

    public void setSoftwareVersionType(SoftwareVersionType softwareVersionType) {
        this.softwareVersionType = softwareVersionType;
    }

    public LocalDateTime getCreatedAt() {
        return createdAt;
    }

    public void setCreatedAt(LocalDateTime createdAt) {
        this.createdAt = createdAt;
    }

    public LocalDateTime getUpdatedAt() {
        return updatedAt;
    }

    public void setUpdatedAt(LocalDateTime updatedAt) {
        this.updatedAt = updatedAt;
    }

    
}
