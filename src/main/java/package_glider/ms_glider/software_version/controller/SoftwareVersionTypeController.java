package package_glider.ms_glider.software_version.controller;

import io.swagger.v3.oas.annotations.Operation;
import java.util.stream.Stream;
import lombok.RequiredArgsConstructor;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.CrossOrigin;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import package_glider.ms_glider.software_version.service.SoftwareVersionTypeService;
import package_glider.ms_glider.software_version.crud.SoftwareVersionTypeRead;
import package_glider.ms_glider.software_version.crud.SoftwareVersionTypeCreate;




@RestController
@RequestMapping("/api/software-version-types")
@CrossOrigin("*")
@RequiredArgsConstructor
public class SoftwareVersionTypeController {
    private final SoftwareVersionTypeService SoftwareVersionTypeService;

    @Operation(summary = "List all software version types")
    @GetMapping
    Stream<SoftwareVersionTypeRead> all() {
        return SoftwareVersionTypeService.list().map(item -> SoftwareVersionTypeRead.from(item));
    }

    @Operation(summary = "Register a new software version type")
    @PostMapping
    SoftwareVersionTypeRead add(final @RequestBody @Validated SoftwareVersionTypeCreate newLocationCategory) {
        return SoftwareVersionTypeService.add(SoftwareVersionTypeCreate.to(newLocationCategory))
                .map(item -> SoftwareVersionTypeRead.from(item)).orElseThrow(() -> new NotFound());
    }
}
