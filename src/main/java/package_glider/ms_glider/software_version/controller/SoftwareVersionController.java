package package_glider.ms_glider.software_version.controller;

import io.swagger.v3.oas.annotations.Operation;

import java.util.List;
import java.util.stream.Stream;
import lombok.RequiredArgsConstructor;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.CrossOrigin;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import package_glider.ms_glider.software_version.crud.SoftwareVersionCreate;
import package_glider.ms_glider.software_version.crud.SoftwareVersionRead;
import package_glider.ms_glider.software_version.service.SoftwareVersionService;
import package_glider.ms_glider.software_version.service.SoftwareVersionTypeService;
import package_glider.ms_glider.software_version.model.SoftwareVersionType;


class NotFound extends RuntimeException {
}



@RestController
@RequestMapping("/api/software-versions")
@CrossOrigin("*")
@RequiredArgsConstructor
public class SoftwareVersionController {
    private final SoftwareVersionService softwareVersionService;
    private final SoftwareVersionTypeService softwareVersionTypeService;

    @Operation(summary = "List all software versions")
    @GetMapping
    Stream<SoftwareVersionRead> all() {
        return softwareVersionService.list().map(item -> SoftwareVersionRead.from(item));
    }

    @Operation(summary = "Get a software version by id")
    @GetMapping("/{id}")
    SoftwareVersionRead result(final @PathVariable Integer id) {
        return softwareVersionService.findById(id).map(item -> SoftwareVersionRead.from(item)).orElseThrow(() -> new NotFound());
    }

    @Operation(summary = "Get software versions by software version type id")
    @GetMapping("/software-version-type/{softwareVersionTypeId}")
    List<SoftwareVersionRead> resultBySoftwareType(final @PathVariable Integer softwareVersionTypeId) {
        return softwareVersionService.findAllBySoftwareVersionTypeId(softwareVersionTypeId);
    }

    @Operation(summary = "Register a new software version")
    @PostMapping
    SoftwareVersionRead add(final @RequestBody @Validated SoftwareVersionCreate softwareVersionCreate) {
        return softwareVersionService.add(SoftwareVersionCreate.to(softwareVersionCreate, softwareVersionType(softwareVersionCreate.getSoftwareVersionTypeId())))
                .map(item -> SoftwareVersionRead.from(item)).orElseThrow(() -> new NotFound());
    }

    private SoftwareVersionType softwareVersionType(final Integer softwareVersionTypeId) {
        return softwareVersionTypeService.findById(softwareVersionTypeId).orElse(null);
    }
}
