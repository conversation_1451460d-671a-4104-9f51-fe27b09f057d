package package_glider.ms_glider.software_version.service;

import java.util.Optional;
import java.util.stream.Stream;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Component;
import package_glider.ms_glider.software_version.model.SoftwareVersionType;
import package_glider.ms_glider.software_version.repository.SoftwareVersionTypeRepository;




@Component
@RequiredArgsConstructor
public final class SoftwareVersionTypeService {
    private final SoftwareVersionTypeRepository softwareVersionTypeRepository;

    public Optional<SoftwareVersionType> findById(final Integer id) {
        return id == null ? Optional.empty() : softwareVersionTypeRepository.findById(id);
    }

    public Stream<SoftwareVersionType> list() {
        return softwareVersionTypeRepository.findAll().stream();
    }

    public Optional<SoftwareVersionType> add(final SoftwareVersionType softwareVersionType) {
        return Optional.of(softwareVersionTypeRepository.save(softwareVersionType));
    }
}