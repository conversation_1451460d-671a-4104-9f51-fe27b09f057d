package package_glider.ms_glider.software_version.service;

import java.util.List;
import java.util.Optional;
import java.util.stream.Collectors;
import java.util.stream.Stream;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;

import package_glider.ms_glider.software_version.crud.SoftwareVersionRead;
import package_glider.ms_glider.software_version.model.SoftwareVersion;
import package_glider.ms_glider.software_version.repository.SoftwareVersionRepository;




@Component
@RequiredArgsConstructor
public class SoftwareVersionService {
    private final SoftwareVersionRepository softwareVersionRepository;

    public Optional<SoftwareVersion> findById(final Integer id) {
        return id == null ? Optional.empty() : softwareVersionRepository.findById(id);
    }

    @Transactional(readOnly = true)
    public List<SoftwareVersionRead> findAllBySoftwareVersionTypeId(final Integer softwareVersionTypeId) {
        return softwareVersionRepository.findBySoftwareVersionTypeId(softwareVersionTypeId)
        .map(SoftwareVersionRead::from)
        .collect(Collectors.toList());
    }

    public Stream<SoftwareVersion> list() {
        return softwareVersionRepository.findAll().stream();
    }

    public Optional<SoftwareVersion> add(final SoftwareVersion softwareVersion) {
        return Optional.of(softwareVersionRepository.save(softwareVersion));
    }
}