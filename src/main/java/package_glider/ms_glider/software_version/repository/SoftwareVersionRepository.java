package package_glider.ms_glider.software_version.repository;

import java.util.stream.Stream;

import org.springframework.data.jpa.repository.JpaRepository;
import package_glider.ms_glider.software_version.model.SoftwareVersion;


public interface SoftwareVersionRepository extends JpaRepository<SoftwareVersion, Integer> {
    Stream<SoftwareVersion> findBySoftwareVersionTypeId(Integer softwareVersionTypeId);
}